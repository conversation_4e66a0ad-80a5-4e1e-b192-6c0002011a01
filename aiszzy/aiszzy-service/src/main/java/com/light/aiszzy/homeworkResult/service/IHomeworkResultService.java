package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 校本作业结果表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkResultService extends IService<HomeworkResultDto> {

    List<HomeworkResultVo> getHomeworkResultListByCondition(HomeworkResultConditionBo condition);

	AjaxResult addHomeworkResult(HomeworkResultBo homeworkResultBo);

	AjaxResult updateHomeworkResult(HomeworkResultBo homeworkResultBo);

    HomeworkResultVo getDetail(String oid);

}

