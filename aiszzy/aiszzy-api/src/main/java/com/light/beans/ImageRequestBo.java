package com.light.beans;

import lombok.Data;
import java.util.List;

@Data
public class ImageRequestBo {
    //至少需要填写其中一项，当同时存在时，优先使用image_base64
    //图片的url地址
    private String imageUrl;
    //base64编码的二进制图片数据
    private String imageBase64;
    //锚点位置[x,y]，以图像左上角为坐标原点	若不填写，或者填写非法值，则以图片中心点作为锚点位置(单位：像素)
    private List<Integer> anchor;

    /**
     * 裁剪图片坐标信息
     */
    private List<ImageCoordinates> coordinates;

    /**
     * 搜题图片
     */
    private String image;
    /**
     * 搜题的关键字
     */
    private String words;
}
