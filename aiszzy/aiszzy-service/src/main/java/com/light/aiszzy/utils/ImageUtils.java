package com.light.aiszzy.utils;

import cn.hutool.core.util.IdUtil;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.geom.Area;
import java.awt.geom.Path2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.*;

/**
 * 图片处理工具类
 */
public class ImageUtils {

    /**
     * 文件临时目录
     */
    private static final String IMAGE_TMP_FILE_PATH = "\\tmp\\";

    /**
     * 定义红色的阈值范围（可根据实际情况调整）
     * 红色通道最小值
     */
    private static final int redMin = 150;
    /**
     * 绿色通道最大值
     */
    private static final int greenMax = 150;
    /**
     * 蓝色通道最大值
     */
    private static final int blueMax = 150;
    /**
     * 黑色判定阈值，RGB都小于该值判定为黑色
     */
    private static final int BLACK_THRESHOLD = 70;
    /**
     * 判定为黑色像素的百分比阈值，超过则认为图片为黑色，否则为非黑色
     */
    private static final double BLACK_PERCENT_THRESHOLD = 0.65;
    /**
     * 旋转角度
     */
    private static final int NUMBER_90 = 90;
    /**
     *
     */
    private static final int NUMBER_180 = 180;

    /**
     * 提取图片中的红色像素，生成新的图片
     *
     * @param originImageUrl 原始图片url路径
     * @return 新图片路径
     * @throws IOException 读取图片时发生IO异常
     */
    public static String retainRedLine(String originImageUrl) throws IOException {
        try {
            // 加载原始图片
            BufferedImage srcBufferedImage = ImageIO.read(new URL(originImageUrl));

            // 创建可修改的 ARGB 图像
            BufferedImage processed = new BufferedImage(srcBufferedImage.getWidth(), srcBufferedImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

            // 遍历所有像素
            for (int y = 0; y < srcBufferedImage.getHeight(null); y++) {
                for (int x = 0; x < srcBufferedImage.getWidth(null); x++) {
                    int pixel = srcBufferedImage.getRGB(x, y);

                    // 分解颜色通道
                    Color color = new Color(pixel, true);
                    int red = color.getRed();
                    int green = color.getGreen();
                    int blue = color.getBlue();

                    // 判断是否为红色（根据阈值）
                    if (red >= redMin && green <= greenMax && blue <= blueMax) {
                        // 保留红色像素（带原始透明度）
                        processed.setRGB(x, y, pixel);
                    } else {
                        // 设置非红色像素为完全透明
                        processed.setRGB(x, y, new Color(0, 0, 0, 0).getRGB());
                    }
                }
            }

            Graphics2D graphics = processed.createGraphics();
            try {
                // 消除锯齿
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            } finally {
                graphics.dispose();
            }

            // 输出图片路径
            String outputPath = IMAGE_TMP_FILE_PATH + IdUtil.randomUUID().replaceAll("-", "") + ".png";

            // 保存处理结果
            ImageIO.write(processed, "PNG", new File(outputPath));

            return outputPath;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        }

    }

    /**
     * 判断图片中是否包含红色像素
     *
     * @param originImageUrl 图片url路径
     * @return true表示图片中包含红色像素，false表示图片中不包含红色像素
     */
    public static boolean containRed(String originImageUrl) throws IOException {
        try {
            BufferedImage srcBufferedImage = ImageIO.read(new URL(originImageUrl));

            // 遍历所有像素
            for (int y = 0; y < srcBufferedImage.getHeight(null); y++) {
                for (int x = 0; x < srcBufferedImage.getWidth(null); x++) {
                    int pixel = srcBufferedImage.getRGB(x, y);

                    // 分解颜色通道
                    Color color = new Color(pixel, true);
                    int red = color.getRed();
                    int green = color.getGreen();
                    int blue = color.getBlue();

                    // 判断是否为红色（根据阈值）
                    if (red >= redMin && green <= greenMax && blue <= blueMax) {
                        // 保留红色像素（带原始透明度）
                        return true;
                    }
                }
            }
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 图片旋转角度检测
     *
     * @param srcImagePath 图片路径
     * @param cropPoints   作业左上角方块标记坐标
     * @return 顺时针旋转角度
     */
    public static int rotateImageAngleDetect(String srcImagePath, Point[] cropPoints) throws IOException {
        int rotateAngle = 0;

        BufferedImage srcImage = ImageIO.read(new File(srcImagePath));

        // 图片宽高
        int width = srcImage.getWidth();
        int height = srcImage.getHeight();

        BufferedImage imddleImage = srcImage;

        // 如果图片宽高比例大于1，则先旋转图片90度
        if (width > height) {
            rotateAngle += 90;
            imddleImage = rotateImage(srcImage, 90);
        }

        // 截取图片上标志图片
        BufferedImage bufferedImage = cropImageWithConvexPolygon(imddleImage, cropPoints);

        // 判断是否为全黑方块，是则认为是四边形，否则认为是三角形 旋转180度
        if (!isMostlyBlack(bufferedImage)) {
            // 旋转图片180度
            rotateImage(bufferedImage, 180);

            rotateAngle += 180;
        }
        return rotateAngle;
    }

    /**
     * 旋转图像
     *
     * @param srcImagePath 原始图像路径
     * @param angle        顺时针旋转角度（90、180、270）
     * @return 旋转后的图像
     * @throws IOException 读取图像时发生错误
     */
    public static BufferedImage rotateImage(String srcImagePath, int angle) throws IOException {
        BufferedImage srcBufferedImage = ImageIO.read(new File(srcImagePath));
        if (angle % 360 == 0) {
            return srcBufferedImage; // 不需要旋转
        }
        return rotateImage(srcBufferedImage, angle);
    }

    /**
     * 旋转图像
     *
     * @param srcBufferedImage 原始图像
     * @param angle            顺时针旋转角度（90、180、270）
     * @return 旋转后的图像
     */
    public static BufferedImage rotateImage(BufferedImage srcBufferedImage, int angle) {

        int w = srcBufferedImage.getWidth();
        int h = srcBufferedImage.getHeight();

        // 旋转角度转弧度
        double radians = Math.toRadians(angle);

        // 计算旋转后图像的尺寸
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));
        int newW = (int) Math.floor(w * cos + h * sin);
        int newH = (int) Math.floor(h * cos + w * sin);

        // 创建旋转后的图像
        BufferedImage rotatedImage = new BufferedImage(newW, newH, srcBufferedImage.getType());
        Graphics2D g2d = rotatedImage.createGraphics();

        // 设置抗锯齿和质量优化
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

        // 平移 + 旋转
        AffineTransform transform = new AffineTransform();
        transform.translate((newW - w) / 2.0, (newH - h) / 2.0);
        transform.rotate(radians, w / 2.0, h / 2.0);

        g2d.drawImage(srcBufferedImage, transform, null);
        g2d.dispose();

        return rotatedImage;
    }

    /**
     * 使用凸多边形裁剪图片
     *
     * @param inputImagePath  输入图片路径
     * @param outputImagePath 输出图片路径
     * @param cropPoints      需要裁剪的点
     * @throws IOException 读取图片或写入图片时发生错误
     */
    public static void cropImageWithConvexPolygon(
            String inputImagePath,
            String outputImagePath,
            Point[] cropPoints) throws IOException {


        BufferedImage sourceImage = ImageIO.read(new File(inputImagePath));
        BufferedImage outputBufferedImage = cropImageWithConvexPolygon(sourceImage, cropPoints);

        ImageIO.write(outputBufferedImage, "png", new File(outputImagePath));
        System.out.println("输出成功: " + outputImagePath);
    }

    /**
     * 裁剪图片
     *
     * @param srcBufferedImage 源图片
     * @param inputPoints      需要裁剪的点
     * @return 裁剪后的图片
     */
    public static BufferedImage cropImageWithConvexPolygon(BufferedImage srcBufferedImage, Point[] inputPoints) {
        if (inputPoints.length < 3) {
            throw new IllegalArgumentException("至少需要三个点才能构建区域");
        }
        // 计算凸包点
        Point[] convexPoints = computeConvexHull(inputPoints);


        int width = srcBufferedImage.getWidth();
        int height = srcBufferedImage.getHeight();

        BufferedImage maskedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = maskedImage.createGraphics();

        // 创建多边形裁剪路径
        Path2D polygon = new Path2D.Double();
        polygon.moveTo(convexPoints[0].x, convexPoints[0].y);
        for (int i = 1; i < convexPoints.length; i++) {
            polygon.lineTo(convexPoints[i].x, convexPoints[i].y);
        }
        polygon.closePath();

        g2d.setClip(new Area(polygon));
        g2d.drawImage(srcBufferedImage, 0, 0, null);
        g2d.dispose();

        // 获取裁剪区域边界并裁切图像
        Rectangle bounds = polygon.getBounds();
        return maskedImage.getSubimage(bounds.x, bounds.y, bounds.width, bounds.height);
    }

    /**
     * 使用 Graham Scan 算法计算凸包
     *
     * @param points 点列表
     * @return 凸包点列表
     */
    public static Point[] computeConvexHull(Point[] points) {
        java.util.List<Point> sorted = new ArrayList<>(Arrays.asList(points));
        sorted.sort(Comparator.<Point>comparingInt(p -> p.x).thenComparingInt(p -> p.y));

        java.util.List<Point> lower = new ArrayList<>();
        for (Point p : sorted) {
            while (lower.size() >= 2 && cross(lower.get(lower.size() - 2), lower.get(lower.size() - 1), p) <= 0) {
                lower.remove(lower.size() - 1);
            }
            lower.add(p);
        }

        List<Point> upper = new ArrayList<>();
        Collections.reverse(sorted);
        for (Point p : sorted) {
            while (upper.size() >= 2 && cross(upper.get(upper.size() - 2), upper.get(upper.size() - 1), p) <= 0) {
                upper.remove(upper.size() - 1);
            }
            upper.add(p);
        }

        lower.remove(lower.size() - 1);
        upper.remove(upper.size() - 1);
        lower.addAll(upper);

        return lower.toArray(new Point[0]);
    }

    /**
     * 计算两个向量的叉积
     * 该函数用于计算从点o到点a的向量与从点o到点b的向量的叉积
     * 叉积的结果可以用于判断点的相对位置关系：
     * - 结果为正：点b在向量oa的左侧
     * - 结果为负：点b在向量oa的右侧
     * - 结果为0：三点共线
     *
     * @param o 基准点，作为向量的起点
     * @param a 第一个向量的终点
     * @param b 第二个向量的终点
     * @return 返回两个向量的叉积结果
     */
    private static int cross(Point o, Point a, Point b) {
        return (a.x - o.x) * (b.y - o.y) - (a.y - o.y) * (b.x - o.x);
    }


    /**
     * 黑色像素占图片比例范围判断为全黑、半黑、纯白，以此区分为长方形、三角形、空
     *
     * @param imagePath 图片路径
     * @return true 为大部分全黑，false 为小部分黑
     */
    public static boolean isMostlyBlack(String imagePath) {
        try {
            BufferedImage image = ImageIO.read(new File(imagePath));
            return isMostlyBlack(image);
        } catch (IOException e) {
            System.err.println("无法读取图像文件: " + e.getMessage());
            return false;
        }
    }

    /**
     * 黑色像素占图片比例范围判断为全黑、半黑、纯白，以此区分为长方形、三角形、空
     *
     * @param image BufferedImage
     * @return true 为大部分全黑，false 为小部分黑
     */
    public static boolean isMostlyBlack(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        int blackPixelCount = 0;
        int totalPixelCount = width * height;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int r = (rgb >> 16) & 0xff;
                int g = (rgb >> 8) & 0xff;
                int b = rgb & 0xff;

                if (r < BLACK_THRESHOLD && g < BLACK_THRESHOLD && b < BLACK_THRESHOLD) {
                    blackPixelCount++;
                }
            }
        }

        double blackRatio = blackPixelCount / (double) totalPixelCount;
        System.out.printf("黑色像素比例: %.2f%%\n", blackRatio * 100);

        return blackRatio > BLACK_PERCENT_THRESHOLD;

    }

    public static void main(String[] args) {
//        try {
//            // System.out.println(containRed("https://aiszzy-1347059756.cos.ap-nanjing.myqcloud.com/fhsljy/20250724/ec74968d-8dc9-416d-808f-3e91c7d2c6bc/Doc1753321670_1.jpg"));
//
//            String srcImagePath = "C:\\Users\\<USER>\\Downloads\\Doc1753423769_1.jpg";
//
//
//            Point[] points = AISzzyConstants.PaperRectInfo.A4_LEFT_TOP.toPoints();
//
//            int angle = rotateImageAngleDetect(srcImagePath, points);
//            System.out.println("旋转角度：" + angle);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
    }
}
