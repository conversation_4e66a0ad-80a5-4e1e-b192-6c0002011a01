<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultAnswerMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="homeworkResultOid" column="homework_result_oid"/>
        <result property="content" column="content"/>
        <result property="smallContent" column="small_content"/>
        <result property="textContent" column="text_content"/>
        <result property="smallPosition" column="small_position"/>
        <result property="questionNum" column="question_num"/>
        <result property="bigNum" column="big_num"/>
        <result property="smallNum" column="small_num"/>
        <result property="orderNum" column="order_num"/>
        <result property="questionTypeId" column="question_type_id"/>
        <result property="questionTypeName" column="question_type_name"/>
        <result property="answer" column="answer"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="sectionId" column="section_id"/>
        <result property="knowledgePointsId" column="knowledge_points_id"/>
        <result property="subject" column="subject"/>
        <result property="grade" column="grade"/>
        <result property="difficultId" column="difficult_id"/>
        <result property="year" column="year"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="positions" column="positions"/>
        <result property="questionOid" column="question_oid"/>
        <result property="studOid" column="stud_oid"/>
        <result property="stuPageNo" column="stu_page_no"/>
        <result property="studClassNo" column="stud_class_no"/>
        <result property="studNo" column="stud_no"/>
        <result property="isCorrect" column="is_correct"/>
        <result property="stuAnswer" column="stu_answer"/>
        <result property="studAnswerUrls" column="stud_answer_urls"/>
        <result property="stuPositions" column="stu_positions"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultAnswerListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo">
		select t.* from (
			select a.* from homework_result_answer as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="homeworkResultOid != null and homeworkResultOid != ''">and homework_result_oid = #{homeworkResultOid}</if>
				<if test="content != null and content != ''">and content = #{content}</if>
				<if test="smallContent != null and smallContent != ''">and small_content = #{smallContent}</if>
				<if test="textContent != null and textContent != ''">and text_content = #{textContent}</if>
				<if test="smallPosition != null and smallPosition != ''">and small_position = #{smallPosition}</if>
				<if test="questionNum != null and questionNum != ''">and question_num = #{questionNum}</if>
				<if test="bigNum != null and bigNum != ''">and big_num = #{bigNum}</if>
				<if test="smallNum != null and smallNum != ''">and small_num = #{smallNum}</if>
				<if test="orderNum != null and orderNum != ''">and order_num = #{orderNum}</if>
				<if test="questionTypeId != null and questionTypeId != ''">and question_type_id = #{questionTypeId}</if>
				<if test="questionTypeName != null and questionTypeName != ''">and question_type_name = #{questionTypeName}</if>
				<if test="answer != null and answer != ''">and answer = #{answer}</if>
				<if test="chapterId != null and chapterId != ''">and chapter_id = #{chapterId}</if>
				<if test="sectionId != null and sectionId != ''">and section_id = #{sectionId}</if>
				<if test="knowledgePointsId != null and knowledgePointsId != ''">and knowledge_points_id = #{knowledgePointsId}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="difficultId != null">and difficult_id = #{difficultId}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="imageUrls != null and imageUrls != ''">and image_urls = #{imageUrls}</if>
				<if test="positions != null and positions != ''">and positions = #{positions}</if>
				<if test="questionOid != null and questionOid != ''">and question_oid = #{questionOid}</if>
				<if test="studOid != null and studOid != ''">and stud_oid = #{studOid}</if>
				<if test="stuPageNo != null">and stu_page_no = #{stuPageNo}</if>
				<if test="studClassNo != null and studClassNo != ''">and stud_class_no = #{studClassNo}</if>
				<if test="studNo != null and studNo != ''">and stud_no = #{studNo}</if>
				<if test="isCorrect != null">and is_correct = #{isCorrect}</if>
				<if test="stuAnswer != null and stuAnswer != ''">and stu_answer = #{stuAnswer}</if>
				<if test="studAnswerUrls != null and studAnswerUrls != ''">and stud_answer_urls = #{studAnswerUrls}</if>
				<if test="stuPositions != null and stuPositions != ''">and stu_positions = #{stuPositions}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>