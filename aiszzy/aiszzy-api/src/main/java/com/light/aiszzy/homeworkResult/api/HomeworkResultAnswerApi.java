package com.light.aiszzy.homeworkResult.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 学生题目答案表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultAnswerApi  {

	/**
	 * 查询学生题目答案表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultAnswer/pageList")
	@ApiOperation(value = "分页查询学生题目答案表",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkResultAnswerVo>> getHomeworkResultAnswerPageListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition);

	/**
	 * 查询所有学生题目答案表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultAnswer/list")
	@ApiOperation(value = "查询所有学生题目答案表",httpMethod = "POST")
	AjaxResult<List<HomeworkResultAnswerVo>> getHomeworkResultAnswerListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition);


	/**
	 * 新增学生题目答案表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultAnswer/add")
	@ApiOperation(value = "新增学生题目答案表",httpMethod = "POST")
	AjaxResult addHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo);

	/**
	 * 修改学生题目答案表
	 * @param homeworkResultAnswerBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResultAnswer/update")
	@ApiOperation(value = "修改学生题目答案表",httpMethod = "POST")
	AjaxResult updateHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo);

	/**
	 * 查询学生题目答案表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResultAnswer/detail")
	@ApiOperation(value = "查询学生题目答案表详情",httpMethod = "GET")
	AjaxResult<HomeworkResultAnswerVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除学生题目答案表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResultAnswer/delete")
	@ApiOperation(value = "删除学生题目答案表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

