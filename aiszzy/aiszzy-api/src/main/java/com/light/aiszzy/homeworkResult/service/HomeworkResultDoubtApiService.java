package com.light.aiszzy.homeworkResult.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homeworkResult.api.HomeworkResultDoubtApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本作业疑问表（影子表，处理作业整张卷子的疑问项）接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkResultDoubtApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkResultDoubtApiService.HomeworkResultDoubtApiFallbackFactory.class)
@Component
public interface HomeworkResultDoubtApiService  extends HomeworkResultDoubtApi {

	@Component
	class HomeworkResultDoubtApiFallbackFactory implements FallbackFactory<HomeworkResultDoubtApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkResultDoubtApiService.HomeworkResultDoubtApiFallbackFactory.class);
		@Override
		public HomeworkResultDoubtApiService create(Throwable cause) {
			HomeworkResultDoubtApiService.HomeworkResultDoubtApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkResultDoubtApiService() {
				public AjaxResult getHomeworkResultDoubtPageListByCondition(HomeworkResultDoubtConditionBo condition){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）查询失败");
				}
				public AjaxResult getHomeworkResultDoubtListByCondition(HomeworkResultDoubtConditionBo condition){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）查询失败");
				}

				public AjaxResult addHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）新增失败");
				}

				public AjaxResult updateHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("校本作业疑问表（影子表，处理作业整张卷子的疑问项）删除失败");
				}
			};
		}
	}
}