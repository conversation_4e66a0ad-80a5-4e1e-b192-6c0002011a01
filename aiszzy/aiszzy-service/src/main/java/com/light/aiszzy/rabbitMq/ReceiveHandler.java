package com.light.aiszzy.rabbitMq;


import cn.hutool.json.JSONUtil;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.apiRequestLog.mapper.ApiRequestLogMapper;
import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.mapper.ResultUploadFileMapper;
import com.light.utils.ApiRequestLogUtil;
import com.light.utils.UploadFileMqUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class ReceiveHandler {

    @Autowired
    private ApiRequestLogMapper apiRequestLogMapper;

    @Autowired
    private ResultUploadFileMapper resultUploadFileMapper;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = ApiRequestLogUtil.API_REQUEST_LOG_LOGIN_LOG_QUEUE, durable = "true"),
            exchange = @Exchange(name = ApiRequestLogUtil.API_REQUEST_LOG_EXCHANGE,
                    ignoreDeclarationExceptions = "true"),
            key = {ApiRequestLogUtil.API_REQUEST_LOG_LOGIN_LOG_QUEUE}
    ))
    public void receiveApiRequestLog(String msg) {
        try {
            ApiRequestLogDto dto = JSONUtil.toBean(msg, ApiRequestLogDto.class);
            apiRequestLogMapper.insert(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = UploadFileMqUtil.UPLADE_FILE_QUEUE, durable = "true"),
            exchange = @Exchange(name = UploadFileMqUtil.UPLADE_FILE_EXCHANGE,
                    ignoreDeclarationExceptions = "true"),
            key = {UploadFileMqUtil.UPLADE_FILE_QUEUE}
    ))
    public void receiveUploadFile(String msg) {
        try {
            ResultUploadFileDto dto = JSONUtil.toBean(msg, ResultUploadFileDto.class);
            resultUploadFileMapper.insert(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
