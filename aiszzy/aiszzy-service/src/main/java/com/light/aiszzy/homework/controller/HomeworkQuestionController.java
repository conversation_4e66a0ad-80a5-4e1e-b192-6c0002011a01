package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.service.IHomeworkQuestionService;

import com.light.aiszzy.homework.api.HomeworkQuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 校本作业题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "校本作业题目信息接口")
public class HomeworkQuestionController implements HomeworkQuestionApi {

    @Autowired
    private IHomeworkQuestionService homeworkQuestionService;

    public AjaxResult<PageInfo<HomeworkQuestionVo>> getHomeworkQuestionPageListByCondition(@RequestBody HomeworkQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkQuestionVo> pageInfo = new PageInfo<>(homeworkQuestionService.getHomeworkQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkQuestionVo>> getHomeworkQuestionListByCondition(@RequestBody HomeworkQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkQuestionService.getHomeworkQuestionListByCondition(condition));
    }

    public AjaxResult addHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo) {
        return homeworkQuestionService.addHomeworkQuestion(homeworkQuestionBo);
    }

    public AjaxResult updateHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo) {
        if (null == homeworkQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkQuestionService.updateHomeworkQuestion(homeworkQuestionBo);
    }

    public AjaxResult<HomeworkQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkQuestionBo homeworkQuestionBo = new HomeworkQuestionBo();
            homeworkQuestionBo.setOid(oid);
            homeworkQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkQuestionService.updateHomeworkQuestion(homeworkQuestionBo);
    }

    @Override
    public AjaxResult changeQuestion(String oid, String oldQuestionOid, String newQuestionOid) {
        return homeworkQuestionService.changeQuestion(oid,oldQuestionOid,newQuestionOid);
    }

    @Override
    public AjaxResult deleteQuestion(String oid, String questionOid) {
        return homeworkQuestionService.deleteQuestion(oid,questionOid);
    }
}
