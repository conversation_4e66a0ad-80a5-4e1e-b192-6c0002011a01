package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.service.IHomeworkBookService;

import com.light.aiszzy.homework.api.HomeworkBookApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 作业本
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "作业本接口")
public class HomeworkBookController implements HomeworkBookApi {

    @Autowired
    private IHomeworkBookService homeworkBookService;

    public AjaxResult<PageInfo<HomeworkBookVo>> getHomeworkBookPageListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkBookVo> pageInfo = new PageInfo<>(homeworkBookService.getHomeworkBookListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkBookVo>> getHomeworkBookListByCondition(@RequestBody HomeworkBookConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkBookService.getHomeworkBookListByCondition(condition));
    }

    @Override
    public AjaxResult getSchoolNotAddHomeworkBookListByCondition(HomeworkBookConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkBookVo> pageInfo = new PageInfo<>(homeworkBookService.getSchoolNotAddHomeworkBookListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());

    }

    public AjaxResult addHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo) {
        return homeworkBookService.addHomeworkBook(homeworkBookBo);
    }

    public AjaxResult updateHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo) {
        if (null == homeworkBookBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkBookService.updateHomeworkBook(homeworkBookBo);
    }

    public AjaxResult<HomeworkBookVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkBookService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkBookBo homeworkBookBo = new HomeworkBookBo();
            homeworkBookBo.setOid(oid);
            homeworkBookBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkBookService.updateHomeworkBook(homeworkBookBo);
    }

    @Override
    public AjaxResult textbookVersions(String grade, String subject) {
        return homeworkBookService.textbookVersions(grade, subject);
    }

    @Override
    public AjaxResult textbook(String versionId,String gradeId) {
        return homeworkBookService.textbook(versionId,gradeId);
    }

    @Override
    public AjaxResult textbookCatalog(String oid) {
        return homeworkBookService.textbookCatalog(oid);
    }

    @Override
    public AjaxResult checkTextbookCatalog(String oid) {
        return homeworkBookService.checkTextbookCatalog(oid);
    }


    @Override
    public AjaxResult<HomeworkBookVo> copyHomeworkBook(String homeworkBookOid) {
        return homeworkBookService.copyHomeworkBook(homeworkBookOid);
    }

    @Override
    public AjaxResult<HomeworkBookVo> practiceBookToHomeworkBook(String practiceBookOid, String userCode, String orgCode) {
        return homeworkBookService.practiceBookToHomeworkBook(practiceBookOid, userCode, orgCode);
    }

    @Override
    public AjaxResult downloadBookZip(String oid) {
        return homeworkBookService.downloadBookZip(oid);
    }

    @Override
    public AjaxResult downloadBookPdf(String oid) {
        return homeworkBookService.downloadBookPdf(oid);
    }

}
