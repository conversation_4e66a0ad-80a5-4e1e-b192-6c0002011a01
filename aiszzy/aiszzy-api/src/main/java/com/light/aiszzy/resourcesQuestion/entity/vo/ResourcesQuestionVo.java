package com.light.aiszzy.resourcesQuestion.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 资源库题目表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class ResourcesQuestionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 资源oid
     */
    private String questionOid;

    /**
     * 题型id
     */
    private String questionTypeId;

    /**
     * 题型名称
     */
    private String questionTypeName;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 启用年份
     */
    private String year;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 难度
     */
    private Long difficultId;

    /**
     * 题目url或文字
     */
    private String quesBody;

    /**
     * 公共题干url或文字
     */
    private String publicQues;

    /**
     * 答案url或文字
     */
    private String quesAnswer;

    /**
     * 解析url或文字
     */
    private String analysisAnswer;

    /**
     * 题目展示类型  0：图片url  1：html文字 
     */
    private Long quesBodyType;

    /**
     * 公共题干展示类型  0：图片url  1：html文字
     */
    private Long publicQuesType;

    /**
     * 答案展示类型  0：图片url  1：html文字 
     */
    private Long quesAnswerType;

    /**
     * 解析展示类型  0：图片url  1：html文字 
     */
    private Long analysisAnswerType;

    /**
     * 来源 学科网xkw，好未来hwl
     */
    private String thirdSourceType;

    /**
     * 外部id
     */
    private String thirdOutId;

    /**
     * 章节ID
     */
    private String chapterId;

    /**
     * 节ID
     */
    private String sectionId;

    /**
     * 知识点
     */
    private String knowledgePointsId;

    /**
     * 题目json内容
     */
    private String questionJson;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 图片 ocr识别
     */
    private String imageOcrText;


    /**
     * 相似题集合
     */
    private List<ResourcesQuestionVo> similarQuestionList;




}
