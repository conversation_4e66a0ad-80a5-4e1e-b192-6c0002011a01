package com.light.aiszzy.userPaper.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户上传每页图片表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class UserPaperPageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 用户上传试卷OID
     */
    private String userPaperOid;

    /**
     * 页码
     */
    private Long pageNo;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 修改后框题个数（实际）
     */
    private Long questionNum;

    /**
     * 已完成框题个数
     */
    private Long finishQuestionNum;

    /**
     * 解析框题个数
     */
    private Long analysisQuestionNum;

    /**
     * 解析结果
     */
    private String analysisJson;

    /**
     * 是否完成  0：未完成 1：完成
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
