package com.light.aiszzy.web.controller.homework;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageBo;
import com.light.aiszzy.homework.entity.bo.HomeworkPageConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkPageVo;
import com.light.aiszzy.homework.service.HomeworkPageApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业每页题目坐标信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@RestController
@Validated
@Api(value = "", tags = "作业每页题目坐标信息接口" )
public class HomeworkPageApiController {
	
    @Autowired
    private HomeworkPageApiService homeworkPageApiService;

	@PostMapping("/homeworkPage/pageList")
	@ApiOperation(value = "分页查询作业每页题目坐标信息",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkPageVo>> getHomeworkPagePageListByCondition(@RequestBody HomeworkPageConditionBo condition){
		return homeworkPageApiService.getHomeworkPagePageListByCondition(condition);
    }

	@PostMapping("/homeworkPage/list")
	@ApiOperation(value = "查询所有作业每页题目坐标信息",httpMethod = "POST")
	public AjaxResult<List<HomeworkPageVo>> getHomeworkPageAllListByCondition(@RequestBody HomeworkPageConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return homeworkPageApiService.getHomeworkPageListByCondition(condition);
	}

	@PostMapping("/homeworkPage/add")
	@ApiOperation(value = "新增作业每页题目坐标信息",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增作业每页题目坐标信息", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo){
		return homeworkPageApiService.addHomeworkPage(homeworkPageBo);
    }

	@PostMapping("/homeworkPage/update")
	@ApiOperation(value = "修改作业每页题目坐标信息",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改作业每页题目坐标信息", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkPage(@Validated @RequestBody HomeworkPageBo homeworkPageBo) {
		return homeworkPageApiService.updateHomeworkPage(homeworkPageBo);
	}

	@GetMapping("/homeworkPage/detail")
	@ApiOperation(value = "查询作业每页题目坐标信息详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkPageId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkPageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkPageApiService.getDetail(oid);
	}

	@GetMapping("/homeworkPage/delete")
	@ApiOperation(value = "删除作业每页题目坐标信息",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除作业每页题目坐标信息", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkPageApiService.delete(oid);
	}
}
