<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="grade" column="grade"/>
        <result property="classId" column="class_id"/>
        <result property="studOid" column="stud_oid"/>
        <result property="studName" column="stud_name"/>
        <result property="pageNos" column="page_nos"/>
        <result property="orcStuNames" column="orc_stu_names"/>
        <result property="ocrStuNos" column="ocr_stu_nos"/>
        <result property="studAnswerUrls" column="stud_answer_urls"/>
        <result property="studAnswerCorrectResult" column="stud_answer_correct_result"/>
        <result property="studAnswerCorrectResultPrint" column="stud_answer_correct_result_print"/>
        <result property="studAnswerPageInfoJson" column="stud_answer_page_info_json"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkResultListByCondition" resultType="com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo">
		select t.* from (
			select a.* from homework_result as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="classId != null and classId != ''">and class_id = #{classId}</if>
				<if test="studOid != null and studOid != ''">and stud_oid = #{studOid}</if>
				<if test="studName != null and studName != ''">and stud_name = #{studName}</if>
				<if test="pageNos != null and pageNos != ''">and page_nos = #{pageNos}</if>
				<if test="orcStuNames != null and orcStuNames != ''">and orc_stu_names = #{orcStuNames}</if>
				<if test="ocrStuNos != null and ocrStuNos != ''">and ocr_stu_nos = #{ocrStuNos}</if>
				<if test="studAnswerUrls != null and studAnswerUrls != ''">and stud_answer_urls = #{studAnswerUrls}</if>
				<if test="studAnswerCorrectResult != null and studAnswerCorrectResult != ''">and stud_answer_correct_result = #{studAnswerCorrectResult}</if>
				<if test="studAnswerCorrectResultPrint != null and studAnswerCorrectResultPrint != ''">and stud_answer_correct_result_print = #{studAnswerCorrectResultPrint}</if>
				<if test="studAnswerPageInfoJson != null and studAnswerPageInfoJson != ''">and stud_answer_page_info_json = #{studAnswerPageInfoJson}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>
</mapper>