package com.light.utils;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;

public class PositionUtil {


    /**
     *  转换成 x_y_w_h
     * @param positions 二维数组
     * @return {@link String }
     */
    public static String covert2Wh(List<List<Integer>> positions) {
        Integer x = positions.get(0).get(0); // x
        Integer y = positions.get(0).get(1); // y
        int width = positions.get(2).get(0) - positions.get(0).get(0);
        int height = positions.get(3).get(1) - positions.get(0).get(1);
        return x + "_" + y + "_" + width + "_" + height;
    }


    /**
     *  根据坐标转换成 二维数组
     * @param position the position x_y_w_h
     * @return {@link List }<{@link List }<{@link Integer }>>
     */
    public static List<List<Integer>> covert2Positions(String position) {

        String[] parts = position.split("_");
        int x = Integer.parseInt(parts[0]); // x
        int y = Integer.parseInt(parts[1]); // y
        int width = Integer.parseInt(parts[2]); // width
        int height = Integer.parseInt(parts[3]); // height

        // 还原 itemPosition
        List<List<Integer>> itemPosition = new ArrayList<>();

        // 点 0 (x, y)
        List<Integer> point0 = new ArrayList<>();
        point0.add(x);
        point0.add(y);
        itemPosition.add(point0);

        // 点 1 (x + width, y)
        List<Integer> point1 = new ArrayList<>();
        point1.add(x + width);
        point1.add(y);
        itemPosition.add(point1);

        // 点 2 (x + width, y + height)
        List<Integer> point2 = new ArrayList<>();
        point2.add(x + width);
        point2.add(y + height);
        itemPosition.add(point2);

        // 点 3 (x, y + height)
        List<Integer> point3 = new ArrayList<>();
        point3.add(x);
        point3.add(y + height);
        itemPosition.add(point3);
        return itemPosition;
    }


    /**
     *  根据第二个重新处理 坐标点
     * @param maxX 最大 X 坐标点
     * @param maxY 最大 Y 坐标点
     * @param positions the position 当前二维坐标
     * @param nextPositions the next position s 下一个坐标
     * @return {@link List }<{@link List }<{@link Integer }>>
     */
    public static List<List<Integer>> adjustListPoints(Integer maxX, Integer maxY, List<List<Integer>> positions, List<List<Integer>> nextPositions) {

        // 右侧两个坐标点 x 轴拓长, 保留两像素
        int newX = maxX - 2;
        if(positions.get(1).get(0) < newX) {
            positions.get(1).set(0, newX);
            positions.get(2).set(0, newX);
        }

        //  下方两个坐标点 y 拓长
        int heightDiff = 2;
        Integer y = maxY;
        if(CollUtil.isNotEmpty(nextPositions)) {
            y = nextPositions.get(0).get(1);
        }
        int newY = y - heightDiff;
        if(positions.get(2).get(1) < newY) {
            positions.get(2).set(1, newY);
            positions.get(3).set(1, newY);
        }
        return positions;
    }
}
