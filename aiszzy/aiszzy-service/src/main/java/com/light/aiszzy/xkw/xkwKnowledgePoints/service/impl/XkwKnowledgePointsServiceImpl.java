package com.light.aiszzy.xkw.xkwKnowledgePoints.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.bo.XkwKnowledgePointsConditionBo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.dto.XkwKnowledgePointsDto;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.mapper.XkwKnowledgePointsMapper;
import com.light.aiszzy.xkw.xkwKnowledgePoints.service.IXkwKnowledgePointsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 知识树接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-15 09:07:37
 */
@Service
public class XkwKnowledgePointsServiceImpl extends ServiceImpl<XkwKnowledgePointsMapper, XkwKnowledgePointsDto> implements IXkwKnowledgePointsService {

	@Resource
	private XkwKnowledgePointsMapper xkwKnowledgePointsMapper;
	
    @Override
	public List<XkwKnowledgePointsVo> getXkwKnowledgePointsListByCondition(XkwKnowledgePointsConditionBo condition) {
        return xkwKnowledgePointsMapper.getXkwKnowledgePointsListByCondition(condition);
	}



}