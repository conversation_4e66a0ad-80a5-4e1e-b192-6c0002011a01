package com.light.aiszzy.homeworkResult.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 校本作业结果详情表(错误试题)
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_result_error_detail")
public class HomeworkResultErrorDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业id
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 作业结果id
	 */
	@TableField("homework_result_oid")
	private String homeworkResultOid;

	/**
	 * 学校code
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 班级id
	 */
	@TableField("class_id")
	private String classId;

	/**
	 * 学生oid
	 */
	@TableField("stud_oid")
	private String studOid;

	/**
	 * 学生错题url
	 */
	@TableField("stud_url")
	private String studUrl;

	/**
	 * 页码
	 */
	@TableField("stu_page_no")
	private Long stuPageNo;

	/**
	 * 关联题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 题号
	 */
	@TableField("question_num")
	private String questionNum;

	/**
	 * 大题号
	 */
	@TableField("big_num")
	private String bigNum;

	/**
	 * 小题号
	 */
	@TableField("small_num")
	private String smallNum;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
