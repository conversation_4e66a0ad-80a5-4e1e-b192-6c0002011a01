package com.light.aiszzy.question.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.question.entity.bo.QuestionWithPracticeBo;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 题目表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface QuestionMapper extends BaseMapper<QuestionDto> {

    List<QuestionVo> getQuestionListByCondition(QuestionConditionBo condition);

    List<QuestionVo> getQuestionListByPracticeBookOid(@Param("practiceBookOid") String practiceBookOid, @Param("practiceBookCatalogOid") String practiceBookCatalogOid);

    List<String> getQuestionSimilarListByPracticeBookOid(@Param("practiceBookOid") String practiceBookOid, @Param("practiceBookCatalogOid") String practiceBookCatalogOid);

    List<QuestionWithPracticeBo> getQuestionWithPracticeListByPracticeBookOid(@Param("practiceBookOid") String practiceBookOid);
}
