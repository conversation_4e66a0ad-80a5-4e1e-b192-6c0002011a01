package com.light.utils;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.Maps;
import com.light.core.exception.WarningException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipInputStream;

/**
 *  解压工具类
 * <AUTHOR>
 * @date 2025/07/10
 */
@Slf4j
public class ZipUtils {


    /**
     *  解压 图片zip 转 map 数据
     * @param inputStream the inputStream 流
     * @return {@link Map }<{@link String }, {@link byte[] }>
     */
    public  static Map<String, String> unImgZip2ImgMap(InputStream inputStream) {

        Map<String, String> result = Maps.newHashMap();
        try(ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            List<String> imgExtNameList = Arrays.asList("png","jpg","jpeg");
            ZipUtil.read(zipInputStream, x-> {


                // zip 文件信息
                String name = x.getName();
                String extName = FileUtil.extName(name);
                if(!imgExtNameList.contains(extName.toLowerCase())) {
                    log.error("【图片压缩包解压】 压缩包内包含非图片类型文件, 文件名称:{}", name);
                    throw new WarningException("压缩包内包含非图片类型文件，无法解析");
                }
                FastByteArrayOutputStream read = IoUtil.read(zipInputStream,false);
                int pageNo = 0;
                try {
                    pageNo = NumberUtil.parseInt(name.substring(0, name.lastIndexOf(".")));
                } catch (NumberFormatException e) {
                    log.error("【图片压缩包解压】 压缩包内图片名称包含非数字内容, 文件名称:{}", name);
                    throw new WarningException("压缩包内图片名称包含非数字内容，无法解析页码");
                }
                result.put(name, Base64.getEncoder().encodeToString(read.toByteArray()));
            });
            return result;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            IoUtil.close(inputStream);
        }
    }
}
