package com.light.aiszzy.homeworkResult.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homeworkResult.api.HomeworkResultApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 校本作业结果表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkResultApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkResultApiService.HomeworkResultApiFallbackFactory.class)
@Component
public interface HomeworkResultApiService  extends HomeworkResultApi {

	@Component
	class HomeworkResultApiFallbackFactory implements FallbackFactory<HomeworkResultApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkResultApiService.HomeworkResultApiFallbackFactory.class);
		@Override
		public HomeworkResultApiService create(Throwable cause) {
			HomeworkResultApiService.HomeworkResultApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkResultApiService() {
				public AjaxResult getHomeworkResultPageListByCondition(HomeworkResultConditionBo condition){
					return AjaxResult.fail("校本作业结果表查询失败");
				}
				public AjaxResult getHomeworkResultListByCondition(HomeworkResultConditionBo condition){
					return AjaxResult.fail("校本作业结果表查询失败");
				}

				public AjaxResult addHomeworkResult(HomeworkResultBo homeworkResultBo){
					return AjaxResult.fail("校本作业结果表新增失败");
				}

				public AjaxResult updateHomeworkResult(HomeworkResultBo homeworkResultBo){
					return AjaxResult.fail("校本作业结果表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("校本作业结果表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("校本作业结果表删除失败");
				}
			};
		}
	}
}