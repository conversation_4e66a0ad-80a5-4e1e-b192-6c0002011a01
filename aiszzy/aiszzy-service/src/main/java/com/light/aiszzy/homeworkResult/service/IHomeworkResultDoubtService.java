package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDoubtDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本作业疑问表（影子表，处理作业整张卷子的疑问项）接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkResultDoubtService extends IService<HomeworkResultDoubtDto> {

    List<HomeworkResultDoubtVo> getHomeworkResultDoubtListByCondition(HomeworkResultDoubtConditionBo condition);

	AjaxResult addHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo);

	AjaxResult updateHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo);

    HomeworkResultDoubtVo getDetail(String oid);

}

