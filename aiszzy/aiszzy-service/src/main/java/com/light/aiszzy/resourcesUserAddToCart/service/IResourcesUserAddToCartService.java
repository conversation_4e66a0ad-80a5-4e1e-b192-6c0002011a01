package com.light.aiszzy.resourcesUserAddToCart.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.resourcesUserAddToCart.entity.dto.ResourcesUserAddToCartDto;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IResourcesUserAddToCartService extends IService<ResourcesUserAddToCartDto> {

    List<ResourcesUserAddToCartVo> getResourcesUserAddToCartListByCondition(ResourcesUserAddToCartConditionBo condition);

	AjaxResult addResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo);

	AjaxResult updateResourcesUserAddToCart(ResourcesUserAddToCartBo resourcesUserAddToCartBo);

    ResourcesUserAddToCartVo getDetail(String oid);

}

