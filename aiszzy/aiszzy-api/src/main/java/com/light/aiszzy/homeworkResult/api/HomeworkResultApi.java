package  com.light.aiszzy.homeworkResult.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 校本作业结果表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultApi  {

	/**
	 * 查询校本作业结果表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResult/pageList")
	@ApiOperation(value = "分页查询校本作业结果表",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkResultVo>> getHomeworkResultPageListByCondition(@RequestBody HomeworkResultConditionBo condition);

	/**
	 * 查询所有校本作业结果表列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResult/list")
	@ApiOperation(value = "查询所有校本作业结果表",httpMethod = "POST")
	AjaxResult<List<HomeworkResultVo>> getHomeworkResultListByCondition(@RequestBody HomeworkResultConditionBo condition);


	/**
	 * 新增校本作业结果表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResult/add")
	@ApiOperation(value = "新增校本作业结果表",httpMethod = "POST")
	AjaxResult addHomeworkResult(@Validated @RequestBody HomeworkResultBo homeworkResultBo);

	/**
	 * 修改校本作业结果表
	 * @param homeworkResultBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@PostMapping("/homeworkResult/update")
	@ApiOperation(value = "修改校本作业结果表",httpMethod = "POST")
	AjaxResult updateHomeworkResult(@Validated @RequestBody HomeworkResultBo homeworkResultBo);

	/**
	 * 查询校本作业结果表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResult/detail")
	@ApiOperation(value = "查询校本作业结果表详情",httpMethod = "GET")
	AjaxResult<HomeworkResultVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除校本作业结果表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:34
	 */
	@GetMapping("/homeworkResult/delete")
	@ApiOperation(value = "删除校本作业结果表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

