package com.light.aiszzy.practiceBook.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 教辅信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 教辅信息表id
	 */
	@ApiModelProperty("教辅信息表id")
	private Long practiceBookId;
	
	/**
	 * 自增主键，唯一标识每一条记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条记录")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	private String code;
	/**
	 * 教辅名称
	 */
	@ApiModelProperty("教辅名称")
	private String name;
	/**
	 * 教辅封面，存储图片的路径或URL
	 */
	@ApiModelProperty("教辅封面，存储图片的路径或URL")
	private String coverImage;
	/**
	 * 教辅简介
	 */
	@ApiModelProperty("教辅简介")
	private String description;
	/**
	 * 出版社 code, 字典转换
	 */
	@ApiModelProperty("出版社 code, 字典转换")
	private String publisher;
	/**
	 * 学科CODE
	 */
	@ApiModelProperty("学科CODE")
	private Integer subject;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * isbn
	 */
	@ApiModelProperty("isbn")
	private String isbn;
	/**
	 * 版本，存储教辅的版本信息
	 */
	@ApiModelProperty("版本，存储教辅的版本信息")
	private Long textBookVersionId;
	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private String year;
	/**
	 * 学期，1上学期，2下学期
	 */
	@ApiModelProperty("学期，1上学期，2下学期")
	private Integer term;
	/**
	 * 教辅系列，存储教辅所属系列（可选）
	 */
	@ApiModelProperty("教辅系列，存储教辅所属系列（可选）")
	private String series;
	/**
	 * 教辅分类，存储教辅的分类信息
	 */
	@ApiModelProperty("教辅分类，存储教辅的分类信息")
	private String category;
	/**
	 * 可见范围，可选值为 0 public（公开）或 1 private（私有），默认为 0 public
	 */
	@ApiModelProperty("可见范围，可选值为 0 public（公开）或 1 private（私有），默认为 0 public")
	private Integer visibility;
	/**
	 * 教辅文件，存储文件的路径或URL
	 */
	@ApiModelProperty("教辅文件，存储文件的路径或URL")
	private String fileUrl;
	/**
	 * 教辅文件类型，1 pdf 2 zip
	 */
	@ApiModelProperty("教辅文件类型，1 pdf 2 zip")
	private Integer fileType;
	/**
	 * 是否是市场化教辅
	 */
	@ApiModelProperty("是否是市场化教辅")
	private Integer isMarketization;
	/**
	 * 是否支持高拍
	 */
	@ApiModelProperty("是否支持高拍")
	private Integer isHighShots;
	/**
	 * 目录文件地址
	 */
	@ApiModelProperty("目录文件地址")
	private String catalogFilePath;
	/**
	 * 目录是否设置，0否1是
	 */
	@ApiModelProperty("目录是否设置，0否1是")
	private Integer catalogStatus;
	/**
	 * 审核状态，1未提交，2审核中 3审核成功 4打回
	 */
	@ApiModelProperty("审核状态，1未提交，2审核中 3审核成功 4打回")
	private Integer reviewStatus;
	/**
	 * 最后审核意见
	 */
	@ApiModelProperty("最后审核意见")
	private String reviewComment;
	/**
	 * 状态 1下架，2上架
	 */
	@ApiModelProperty("状态 1下架，2上架")
	private Integer status;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 已完成框题个数
	 */
	@ApiModelProperty("已完成框题个数")
	private Long finishQuestionNum;
	/**
	 * 需要框题所有个数
	 */
	@ApiModelProperty("需要框题所有个数")
	private Long totalQuestionNum;
	/**
	 * 学科网智书id，空为自建
	 */
	@ApiModelProperty("学科网智书id，空为自建")
	private String xkwZsId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


}
