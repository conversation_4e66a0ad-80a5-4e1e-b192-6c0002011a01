package com.light.aiszzy.userPaper.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 资源库试卷表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_paper_question")
public class UserPaperQuestionDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 学校code
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 上传试卷oid
	 */
	@TableField("user_paper_oid")
	private String userPaperOid;

	/**
	 * 学生上传试卷page的oid
	 */
	@TableField("user_paper_page_oid")
	private String userPaperPageOid;

	/**
	 * 校本资源题目oid
	 */
	@TableField("school_resource_question_oid")
	private String schoolResourceQuestionOid;

	/**
	 * 图片地址
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 示例x_96,y_496,w_1100,h_275
	 */
	@TableField("position")
	private String position;

	/**
	 * 标注状态 0 未标注 1 已标注
	 */
	@TableField("mark_status")
	private Integer markStatus;

	/**
	 * 页面
	 */
	@TableField("page_num")
	private Long pageNum;

	/**
	 * 题目排序
	 */
	@TableField("order_num")
	private Long orderNum;

	/**
	 * 学科网xkw，好未来hwl等
	 */
	@TableField("third_source_type")
	private String thirdSourceType;

	/**
	 * 添加外部id
	 */
	@TableField("third_out_id")
	private String thirdOutId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
