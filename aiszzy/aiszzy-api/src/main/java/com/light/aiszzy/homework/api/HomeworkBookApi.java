package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkBookApi  {

	/**
	 * 查询作业本列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkBook/pageList")
	@ApiOperation(value = "分页查询作业本",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkBookVo>> getHomeworkBookPageListByCondition(@RequestBody HomeworkBookConditionBo condition);

	/**
	 * 查询所有作业本列表
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkBook/list")
	@ApiOperation(value = "查询所有作业本",httpMethod = "POST")
	AjaxResult<List<HomeworkBookVo>> getHomeworkBookListByCondition(@RequestBody HomeworkBookConditionBo condition);

	/**
	 * 查询 作业本列表
	 *
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@PostMapping("/homeworkBook/listSchoolNotAdd")
	@ApiOperation(value = "分页查询未添加学校作业本", httpMethod = "POST")
	AjaxResult getSchoolNotAddHomeworkBookListByCondition(@RequestBody HomeworkBookConditionBo condition);


	/**
	 * 新增作业本
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkBook/add")
	@ApiOperation(value = "新增作业本",httpMethod = "POST")
	AjaxResult addHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo);

	/**
	 * 修改作业本
	 * @param homeworkBookBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@PostMapping("/homeworkBook/update")
	@ApiOperation(value = "修改作业本",httpMethod = "POST")
	AjaxResult updateHomeworkBook(@Validated @RequestBody HomeworkBookBo homeworkBookBo);

	/**
	 * 查询作业本详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkBook/detail")
	@ApiOperation(value = "查询作业本详情",httpMethod = "GET")
	AjaxResult<HomeworkBookVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业本
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-07 17:24:33
	 */
	@GetMapping("/homeworkBook/delete")
	@ApiOperation(value = "删除作业本",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	@GetMapping("/homeworkBook/textbookVersions")
	AjaxResult textbookVersions(@RequestParam("grade") String grade, @RequestParam("subject") String subject);

	@GetMapping("/homeworkBook/textbook")
	AjaxResult textbook(@RequestParam("versionId") String versionId,@RequestParam(value = "gradeId",required = false) String gradeId);

	@GetMapping("/homeworkBook/textbookCatalog")
	AjaxResult textbookCatalog(@RequestParam("oid") String oid);

	@GetMapping("/homeworkBook/checkTextbookCatalog")
	AjaxResult checkTextbookCatalog(@RequestParam("oid") String oid);

	/**
	 * 复制 作业本
	 *
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@GetMapping("/homeworkBook/copyHomeworkBook")
	AjaxResult<HomeworkBookVo> copyHomeworkBook(@RequestParam("homeworkBookOid") String oid);

	/**
	 * 复制 教辅到作业本
	 *
	 * @param oid practiceBookOid
	 * @param userCode 用户编码
	 * @param orgCode 组织编码
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@GetMapping("/homeworkBook/practiceBookToHomeworkBook")
	AjaxResult practiceBookToHomeworkBook(@RequestParam("practiceBookOid") String oid, @RequestParam("userCode") String userCode, @RequestParam("orgCode") String orgCode);


	/**
	 * 下载 作业本
	 *
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@GetMapping("/homeworkBook/downloadBookZip")
	@ApiOperation(value = "下载 作业本", httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult downloadBookZip(@RequestParam("oid") String oid);


	/**
	 * 下载 作业本
	 *
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-03-28 16:01:05
	 */
	@GetMapping("/homeworkBook/downloadBookPdf")
	@ApiOperation(value = "下载作业本答案合并一个pdf", httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult downloadBookPdf(@RequestParam("oid") String oid);
}

