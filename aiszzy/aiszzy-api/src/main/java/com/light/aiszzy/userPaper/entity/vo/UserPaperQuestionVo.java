package com.light.aiszzy.userPaper.entity.vo;

import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 资源库试卷表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@Data
public class UserPaperQuestionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 学校code
     */
    private String orgCode;

    /**
     * 上传试卷oid
     */
    private String userPaperOid;

    /**
     * 学生上传试卷page的oid
     */
    private String userPaperPageOid;

    /**
     * 校本资源题目oid
     */
    private String schoolResourceQuestionOid;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 示例x_96,y_496,w_1100,h_275
     */
    private String position;

    /**
     * 标注状态 0 未标注 1 已标注
     */
    private Integer markStatus;

    /**
     * 页面
     */
    private Long pageNum;

    /**
     * 题目排序
     */
    private Long orderNum;

    /**
     * 学科网xkw，好未来hwl等
     */
    private String thirdSourceType;

    /**
     * 添加外部id
     */
    private String thirdOutId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     *  校本题目信息
     */
    private SchoolResourcesQuestionVo schoolResourcesQuestion;

}
