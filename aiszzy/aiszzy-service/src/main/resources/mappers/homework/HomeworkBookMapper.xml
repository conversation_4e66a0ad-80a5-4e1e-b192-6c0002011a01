<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkBookMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkBookDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="name" column="name"/>
        <result property="orgCode" column="org_code"/>
        <result property="subject" column="subject"/>
        <result property="grade" column="grade"/>
        <result property="year" column="year"/>
        <result property="term" column="term"/>
        <result property="textBookVersionId" column="text_book_version_id"/>
        <result property="textBookId" column="text_book_id"/>
        <result property="exerciseType" column="exercise_type"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="num" column="num"/>
        <result property="status" column="status"/>
        <result property="hasNoConfirm" column="has_no_confirm"/>
        <result property="isCompleted" column="is_completed"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceOid" column="source_oid"/>
        <result property="createSource" column="create_source"/>
        <result property="homeworkPdfUrl" column="homework_pdf_url"/>
        <result property="homeworkAnswerPdfUrl" column="homework_answer_pdf_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkBookListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkBookVo">
		select t.* from (
			select a.* from homework_book as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="name != null and name != ''">and name = #{name}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="year != null and year != ''">and year = #{year}</if>
				<if test="term != null">and term = #{term}</if>
				<if test="textBookVersionId != null">and text_book_version_id = #{textBookVersionId}</if>
				<if test="textBookId != null">and text_book_id = #{textBookId}</if>
				<if test="exerciseType != null">and exercise_type = #{exerciseType}</if>
				<if test="coverUrl != null and coverUrl != ''">and cover_url = #{coverUrl}</if>
				<if test="num != null">and num = #{num}</if>
				<if test="status != null">and status = #{status}</if>
				<if test="hasNoConfirm != null">and has_no_confirm = #{hasNoConfirm}</if>
				<if test="isCompleted != null">and is_completed = #{isCompleted}</if>
				<if test="sourceType != null">and source_type = #{sourceType}</if>
				<if test="sourceOid != null and sourceOid != ''">and source_oid = #{sourceOid}</if>
				<if test="createSource != null and createSource != ''">and create_source = #{createSource}</if>
				<if test="homeworkPdfUrl != null and homeworkPdfUrl != ''">and homework_pdf_url = #{homeworkPdfUrl}</if>
				<if test="homeworkAnswerPdfUrl != null and homeworkAnswerPdfUrl != ''">and homework_answer_pdf_url = #{homeworkAnswerPdfUrl}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="isDelete != null">and is_delete = #{isDelete}</if>
		    </where>
	</select>

	<select id="getCatalogAndHomeworkForDownload" resultType="map">
		SELECT b.oid, b.parent_oid parentOid, b.name, c.name homeworkName, c.homework_pdf_url pdfUrl, c.homework_answer_pdf_url answerUrl
		FROM homework_book_catalog_info a
				 left join homework_book_catalog b on a.catalog_oid = b.oid
				 left join homework c on a.homework_oid = c.oid
		where b.book_oid = #{bookOid}
		  and c.pdf_url is not null
		  and a.is_delete = 0
		  and b.is_delete = 0
		  and c.is_delete = 0
		ORDER BY a.order_num asc, a.id asc, b.order_num asc, b.id asc
	</select>

</mapper>