package com.light.aiszzy.schoolResourcesQuestion.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionConditionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.bo.SchoolResourcesQuestionBo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionService;

import com.light.aiszzy.schoolResourcesQuestion.api.SchoolResourcesQuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 资源库题目表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "资源库题目表接口")
public class SchoolResourcesQuestionController implements SchoolResourcesQuestionApi {

    @Autowired
    private ISchoolResourcesQuestionService schoolResourcesQuestionService;

    public AjaxResult<PageInfo<SchoolResourcesQuestionVo>> getSchoolResourcesQuestionPageListByCondition(@RequestBody SchoolResourcesQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<SchoolResourcesQuestionVo> pageInfo = new PageInfo<>(schoolResourcesQuestionService.getSchoolResourcesQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<SchoolResourcesQuestionVo>> getSchoolResourcesQuestionListByCondition(@RequestBody SchoolResourcesQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(schoolResourcesQuestionService.getSchoolResourcesQuestionListByCondition(condition));
    }

    public AjaxResult addSchoolResourcesQuestion(@Validated @RequestBody SchoolResourcesQuestionBo schoolResourcesQuestionBo) {
        return schoolResourcesQuestionService.addSchoolResourcesQuestion(schoolResourcesQuestionBo);
    }

    public AjaxResult updateSchoolResourcesQuestion(@Validated @RequestBody SchoolResourcesQuestionBo schoolResourcesQuestionBo) {
        if (null == schoolResourcesQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return schoolResourcesQuestionService.updateSchoolResourcesQuestion(schoolResourcesQuestionBo);
    }

    public AjaxResult<SchoolResourcesQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(schoolResourcesQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            SchoolResourcesQuestionBo schoolResourcesQuestionBo = new SchoolResourcesQuestionBo();
            schoolResourcesQuestionBo.setOid(oid);
            schoolResourcesQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return schoolResourcesQuestionService.updateSchoolResourcesQuestion(schoolResourcesQuestionBo);
    }
}
