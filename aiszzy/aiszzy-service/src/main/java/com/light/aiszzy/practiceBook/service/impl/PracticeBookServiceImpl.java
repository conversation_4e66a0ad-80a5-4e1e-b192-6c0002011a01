package com.light.aiszzy.practiceBook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookReviewBo;
import com.light.aiszzy.practiceBook.service.IPracticeBookCatalogService;
import com.light.aiszzy.practiceBook.service.IPracticeBookPageService;
import com.light.aiszzy.practiceBook.service.IPracticeBookReviewService;
import com.light.core.exception.WarningException;
import com.light.enums.PracticeBookReviewStatus;
import com.light.enums.PracticeBookStatus;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.practiceBook.entity.dto.PracticeBookDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookService;
import com.light.aiszzy.practiceBook.mapper.PracticeBookMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 教辅信息表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class PracticeBookServiceImpl extends ServiceImpl<PracticeBookMapper, PracticeBookDto> implements IPracticeBookService {

	@Resource
	private PracticeBookMapper practiceBookMapper;

	@Resource
	private IPracticeBookCatalogService practiceBookCatalogService;

	@Resource
	private IPracticeBookPageService practiceBookPageService;

	@Resource
	private IPracticeBookReviewService practiceBookReviewService;

    @Override
	public List<PracticeBookVo> getPracticeBookListByCondition(PracticeBookConditionBo condition) {
        return practiceBookMapper.getPracticeBookListByCondition(condition);
	}

	@Override
	public AjaxResult addPracticeBook(PracticeBookBo practiceBookBo) {
		PracticeBookDto practiceBook = new PracticeBookDto();
		BeanUtils.copyProperties(practiceBookBo, practiceBook);
		practiceBook.setIsDelete(StatusEnum.NOTDELETE.getCode());
		practiceBook.setOid(IdUtil.simpleUUID());
		if(save(practiceBook)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updatePracticeBook(PracticeBookBo practiceBookBo) {
		LambdaQueryWrapper<PracticeBookDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookDto::getOid, practiceBookBo.getOid());
		PracticeBookDto practiceBook = getOne(lqw);
		Long id = practiceBook.getId();
		if(practiceBook == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(practiceBookBo, practiceBook);
		practiceBook.setId(id);
		if(updateById(practiceBook)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PracticeBookVo getDetail(String oid) {
		LambdaQueryWrapper<PracticeBookDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookDto::getOid, oid);
		PracticeBookDto practiceBook = getOne(lqw);
	    PracticeBookVo practiceBookVo = new PracticeBookVo();
		if(practiceBook != null){
			BeanUtils.copyProperties(practiceBook, practiceBookVo);
		}
		return practiceBookVo;
	}

	@Override
	public PracticeBookVo queryByOid(String oid) {
		QueryWrapper<PracticeBookDto> lqw = new QueryWrapper<>();
		lqw.lambda().eq(PracticeBookDto::getOid, oid);
		lqw.lambda().eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		PracticeBookDto practiceBookDto = this.practiceBookMapper.selectOne(lqw);
		if(practiceBookDto == null){
			return null;
		}
		return BeanUtil.toBean(practiceBookDto, PracticeBookVo.class);
	}

	@Override
	public boolean updateCatalogStatusByOid(String oid, int catalogStatus) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getCatalogStatus, catalogStatus);
		return this.update(updateWrapper);
	}

	@Override
	public AjaxResult updateCatalogPathByOid(String oid, String catalogFilePath) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getCatalogFilePath, catalogFilePath);
		this.update(updateWrapper);
		return AjaxResult.success();
	}

	@Override
	public boolean updateFileInfoByOid(String oid, Integer fileType, String fileUrl) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getFileType, fileType);
		updateWrapper.lambda().set(PracticeBookDto::getFileUrl, fileUrl);
		return this.update(updateWrapper);
	}

	@Override
	public boolean updateQuestionNumInfoByOid(String practiceBookOid, long totalQuestionNum, long finishQuestionNum) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, practiceBookOid);
		updateWrapper.lambda().set(PracticeBookDto::getTotalQuestionNum, totalQuestionNum);
		updateWrapper.lambda().set(PracticeBookDto::getFinishQuestionNum, finishQuestionNum);
		return this.update(updateWrapper);
	}

	@Override
	public boolean addTotalQuestionNumByOid(String practiceBookOid, long totalQuestionNum) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, practiceBookOid);
		updateWrapper.lambda().setSql("total_question_num = ifnull(total_question_num, 0) + "+ totalQuestionNum);
		return this.update(updateWrapper);
	}

	@Override
	public boolean updateIsHighShotsByOid(String oid, Integer isHighShots) {
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getIsHighShots, isHighShots);
		return this.update(updateWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult deleteByOid(String oid) {

		// 删除教辅信息
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		this.update(updateWrapper);

		// 删除目录
		this.practiceBookCatalogService.deleteByPracticeBookOid(oid);

		// 删除分页
		this.practiceBookPageService.deleteByPracticeBookOid(oid);

		return AjaxResult.success();
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult commit(String oid) {
		PracticeBookVo practiceBookVo = Optional.ofNullable(this.queryByOid(oid)).orElseThrow(()-> new WarningException("教辅不存在"));
		Integer reviewStatus = practiceBookVo.getReviewStatus();
        if (PracticeBookReviewStatus.NOT_SUBMITTED.getCode() != reviewStatus && PracticeBookReviewStatus.REJECTED.getCode() != reviewStatus) {
			return AjaxResult.fail("状态非待提交状态，无法进行操作");
        }
		// 校验是否标注完成
		if(!practiceBookVo.getFinishQuestionNum().equals(practiceBookVo.getTotalQuestionNum())) {
			return AjaxResult.fail("教辅未标注结束，无法进行操作");
		}

		// 更新状态
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getReviewStatus, PracticeBookReviewStatus.IN_REVIEW.getCode());
		this.update(updateWrapper);

		// 增加状态记录
		PracticeBookReviewBo bo = new PracticeBookReviewBo();
		bo.setPracticeBookOid(oid);
		bo.setStatus((long) PracticeBookReviewStatus.IN_REVIEW.getCode());
		this.practiceBookReviewService.addPracticeBookReview(bo);

		return AjaxResult.success();
	}



	@Override
	public AjaxResult review(String oid, Integer reviewStatus, String reviewComment) {
		PracticeBookVo practiceBookVo = Optional.ofNullable(this.queryByOid(oid)).orElseThrow(()-> new WarningException("教辅不存在"));
		if (PracticeBookReviewStatus.IN_REVIEW.getCode() != practiceBookVo.getReviewStatus()) {
			return AjaxResult.fail("状态非审核中状态，无法进行操作");
		}

		// 更新状态
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getReviewStatus, reviewStatus);
		updateWrapper.lambda().set(PracticeBookDto::getReviewComment, reviewComment);
		this.update(updateWrapper);

		// 增加状态记录
		PracticeBookReviewBo bo = new PracticeBookReviewBo();
		bo.setPracticeBookOid(oid);
		bo.setStatus(reviewStatus.longValue());
		this.practiceBookReviewService.addPracticeBookReview(bo);

		return AjaxResult.success();
	}

	@Override
	public AjaxResult updateStatusByOid(String oid, Integer status) {
		PracticeBookVo practiceBookVo = Optional.ofNullable(this.queryByOid(oid)).orElseThrow(()-> new WarningException("教辅不存在"));

		// 发布
		if(status == PracticeBookStatus.PUBLISHED.getCode()) {

			// 校验是否已经发布, 未进行发布
			if(PracticeBookStatus.PUBLISHED.getCode() == practiceBookVo.getStatus()) {
				return AjaxResult.fail("该教辅已发布");
			}
			//  校验是否审核通过
			if (PracticeBookReviewStatus.APPROVED.getCode() != practiceBookVo.getReviewStatus()) {
				return AjaxResult.fail("该教辅未审核成功");
			}
		}


		// 更新状态
		UpdateWrapper<PracticeBookDto> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda().eq(PracticeBookDto::getOid, oid);
		updateWrapper.lambda().set(PracticeBookDto::getStatus, status);
		this.update(updateWrapper);

		return AjaxResult.success();
	}

	@Override
	public AjaxResult incrementFinishQuestionNum(String practiceBookOid) {
		if (practiceBookOid == null || practiceBookOid.trim().isEmpty()) {
			return AjaxResult.fail("教辅OID不能为空");
		}

		// 使用数据库级别的CAS操作保证并发安全
		LambdaUpdateWrapper<PracticeBookDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookDto::getOid, practiceBookOid)
				.eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.setSql("finish_question_num = finish_question_num + 1");

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("教辅完成题目数量更新成功");
		} else {
			return AjaxResult.fail("教辅完成题目数量更新失败");
		}
	}

	@Override
	public AjaxResult decrementTotalQuestionNum(String practiceBookOid) {
		if (practiceBookOid == null || practiceBookOid.trim().isEmpty()) {
			return AjaxResult.fail("教辅OID不能为空");
		}

		// 先获取当前记录，检查total_question_num值
		PracticeBookDto currentBook = getOne(new LambdaQueryWrapper<PracticeBookDto>()
				.eq(PracticeBookDto::getOid, practiceBookOid)
				.eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));

		if (currentBook == null) {
			return AjaxResult.fail("教辅记录不存在");
		}

		if (currentBook.getTotalQuestionNum() == null || currentBook.getTotalQuestionNum() <= 0) {
			return AjaxResult.fail("教辅总题目数量已为0，无法继续减少");
		}

		// 计算新的值，避免在SQL中进行可能导致下溢的减法操作
		long newTotalQuestionNum = currentBook.getTotalQuestionNum() - 1;

		// 使用乐观锁更新，确保并发安全
		LambdaUpdateWrapper<PracticeBookDto> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(PracticeBookDto::getOid, practiceBookOid)
				.eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(PracticeBookDto::getTotalQuestionNum, currentBook.getTotalQuestionNum()) // 乐观锁
				.set(PracticeBookDto::getTotalQuestionNum, newTotalQuestionNum);

		boolean updateResult = update(updateWrapper);
		if (updateResult) {
			return AjaxResult.success("教辅总题目数量减少成功");
		} else {
			return AjaxResult.fail("教辅总题目数量减少失败，可能数据已被其他操作修改");
		}
	}
}