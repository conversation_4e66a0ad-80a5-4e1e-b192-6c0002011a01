package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDoubtDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultDoubtService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultDoubtMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本作业疑问表（影子表，处理作业整张卷子的疑问项）接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkResultDoubtServiceImpl extends ServiceImpl<HomeworkResultDoubtMapper, HomeworkResultDoubtDto> implements IHomeworkResultDoubtService {

	@Resource
	private HomeworkResultDoubtMapper homeworkResultDoubtMapper;
	
    @Override
	public List<HomeworkResultDoubtVo> getHomeworkResultDoubtListByCondition(HomeworkResultDoubtConditionBo condition) {
        return homeworkResultDoubtMapper.getHomeworkResultDoubtListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo) {
		HomeworkResultDoubtDto homeworkResultDoubt = new HomeworkResultDoubtDto();
		BeanUtils.copyProperties(homeworkResultDoubtBo, homeworkResultDoubt);
		homeworkResultDoubt.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkResultDoubt.setOid(IdUtil.simpleUUID());
		if(save(homeworkResultDoubt)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkResultDoubt(HomeworkResultDoubtBo homeworkResultDoubtBo) {
		LambdaQueryWrapper<HomeworkResultDoubtDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDoubtDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDoubtDto::getOid, homeworkResultDoubtBo.getOid());
		HomeworkResultDoubtDto homeworkResultDoubt = getOne(lqw);
		Long id = homeworkResultDoubt.getId();
		if(homeworkResultDoubt == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkResultDoubtBo, homeworkResultDoubt);
		homeworkResultDoubt.setId(id);
		if(updateById(homeworkResultDoubt)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkResultDoubtVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkResultDoubtDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDoubtDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDoubtDto::getOid, oid);
		HomeworkResultDoubtDto homeworkResultDoubt = getOne(lqw);
	    HomeworkResultDoubtVo homeworkResultDoubtVo = new HomeworkResultDoubtVo();
		if(homeworkResultDoubt != null){
			BeanUtils.copyProperties(homeworkResultDoubt, homeworkResultDoubtVo);
		}
		return homeworkResultDoubtVo;
	}

}