package com.light.aiszzy.userPaper.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 用户上传每页图片表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class UserPaperPageBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户上传每页图片表id
	 */
	@ApiModelProperty("用户上传每页图片表id")
	private Long userPaperPageId;
	
	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 用户上传试卷OID
	 */
	@ApiModelProperty("用户上传试卷OID")
	private String userPaperOid;
	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Long pageNo;
	/**
	 * 图片地址
	 */
	@ApiModelProperty("图片地址")
	private String imageUrl;
	/**
	 * 修改后框题个数（实际）
	 */
	@ApiModelProperty("修改后框题个数（实际）")
	private Long questionNum;
	/**
	 * 已完成框题个数
	 */
	@ApiModelProperty("已完成框题个数")
	private Long finishQuestionNum;
	/**
	 * 解析框题个数
	 */
	@ApiModelProperty("解析框题个数")
	private Long analysisQuestionNum;
	/**
	 * 解析结果
	 */
	@ApiModelProperty("解析结果")
	private String analysisJson;
	/**
	 * 是否完成  0：未完成 1：完成
	 */
	@ApiModelProperty("是否完成  0：未完成 1：完成")
	private Integer status;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 校本题目信息
	 */
	private List<UserPaperQuestionBo> userPaperQuestionList;
}
