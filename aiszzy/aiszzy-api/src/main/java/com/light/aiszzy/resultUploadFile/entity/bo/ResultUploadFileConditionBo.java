package com.light.aiszzy.resultUploadFile.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 扫描上传图片
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-19 14:10:33
 */
@Data
public class ResultUploadFileConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;

	/**
	 * 通过设备信息获取的学校CODE
	 */
	@ApiModelProperty("通过设备信息获取的学校CODE")
	private String orgCode;

	/**
	 * 存放地址1
	 */
	@ApiModelProperty("存放地址1")
	private String pathOne;

	/**
	 * 存放地址2
	 */
	@ApiModelProperty("存放地址2")
	private String pathTwo;

	/**
	 * 设备号，硬件序列号
	 */
	@ApiModelProperty("设备号，硬件序列号")
	private String hardwareCode;

	/**
	 * 是否识别处理 0未处理，1已经处理
	 */
	@ApiModelProperty("是否识别处理 0未处理，1已经处理")
	private Long isDeal;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
