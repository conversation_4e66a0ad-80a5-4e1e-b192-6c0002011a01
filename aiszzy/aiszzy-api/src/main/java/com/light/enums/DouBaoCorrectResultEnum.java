package com.light.enums;

/**
 * 豆包回复批改结果枚举
 */
public enum DouBaoCorrectResultEnum {

    CORRECT("1", "豆包回复批改判定正确"),
    WRONG("2", "豆包回复批改判定错误"),
    UNKNOWN("3", "豆包异常");

    private final String code;

    private final String reason;

    DouBaoCorrectResultEnum(String code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public String getCode() {
        return code;
    }

    public String getReason() {
        return reason;
    }

}
