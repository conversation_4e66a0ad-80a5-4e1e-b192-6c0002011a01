package com.light.aiszzy.hwlOpen.service;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.light.contants.ConstantsInteger;
import com.light.beans.ImageRequestBo;
import com.light.aiszzy.hwlOpen.tal.ailab.enums.RequestMethod;
import com.light.aiszzy.hwlOpen.tal.ailab.sign.SendSignHttp;
import com.light.aiszzy.hwlOpen.tal.ailab.util.DateUtil;
import com.light.core.entity.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;

import static com.light.aiszzy.hwlOpen.tal.ailab.util.HttpUtil.*;


/**
 * 好未来工具类
 * 
 * 参考XkwUtils的简单模式，使用Java 8特性优化代码
 * 
 * <AUTHOR>
 * @date 2025/7/8
 */
@Slf4j
@Component
public class HwlService {

    @Value("${tal.access.key.id:}")
    private String accessKeyId;

    @Value("${tal.access.key.secret:}")
    private String accessKeySecret;

    /**
     * 好未来自动框选API地址
     */
    private static final String AUTOMATIC_BOX_URL = "http://openai.100tal.com/aiimage/ocr/automatic-box";

    /**
     * 通用HTTP请求处理器
     * 使用函数式接口提高代码复用性
     */
    private Optional<JSONObject> executeRequest(String url, Map<String, Object> params,
            Function<Map<String, Object>, HttpResponse> requestExecutor) {
        try {
            HttpResponse response = requestExecutor.apply(params);
            String responseJson = EntityUtils.toString(response.getEntity(), Charset.defaultCharset());
            log.debug("HWL API: {} response: {}", url, responseJson);
            return StringUtils.isNotBlank(responseJson) 
                ? Optional.ofNullable(JSONObject.parseObject(responseJson))
                : Optional.empty();
        } catch (Exception e) {
            log.error("HWL API: {} request failed", url, e);
            throw new RuntimeException("HWL API: " + url + " request failed: " + e.getMessage());
        }
    }

    /**
     * 发送POST请求
     */
    private Optional<JSONObject> doPost(String url, Map<String, Object> params) {
        return executeRequest(url, params, bodyParams -> {
            try {
                Map<String, Object> urlParams = new HashMap<>();
                Date timestamp = DateUtil.getCurrentDate();
                
                return SendSignHttp.sendRequest(
                    accessKeyId,
                    accessKeySecret,
                    timestamp,
                    url,
                    urlParams,
                    bodyParams,
                    RequestMethod.POST,
                    APPLICATION_JSON
                );
            } catch (Exception e) {
                throw new RuntimeException("发送请求失败", e);
            }
        });
    }

    /**
     * 提取响应数据的通用方法
     * 使用Optional优化空值处理（JDK8兼容版本）
     */
    private Optional<AjaxResult> extractResponseData(Optional<JSONObject> responseOpt, String url, String requestInfo) {
        Optional<AjaxResult> result = responseOpt
            .filter(json -> Objects.equals(json.get("code"), ConstantsInteger.TAL_CODE_SUCCESS))
            .map(this::buildSuccessResult);

        // JDK8兼容：使用isPresent()替代or()方法
        if (!result.isPresent()) {
            responseOpt.ifPresent(json -> {
                log.error("HWL API: {}, request failed, {}, response: {}", url, requestInfo, json);
            });
            // 构建失败结果
            return responseOpt.map(this::buildFailResult);
        }

        return result;
    }

    /**
     * 构建成功结果
     */
    private AjaxResult buildSuccessResult(JSONObject json) {
        JSONObject data = json.getJSONObject("data");
        AjaxResult<JSONObject> result = new AjaxResult<>();
        result.setData(data);
        result.setCode(HttpStatus.HTTP_OK);
        result.setSuccess(true);
        result.setMsg(json.getString("msg"));
        return result;
    }

    /**
     * 构建失败结果
     */
    private AjaxResult buildFailResult(JSONObject json) {
        AjaxResult<JSONObject> result = new AjaxResult<>();
        result.setCode(HttpStatus.HTTP_INTERNAL_ERROR);
        result.setSuccess(false);
        result.setMsg(json.getString("msg"));
        return result;
    }

    /**
     * 处理自动框选请求
     *
     * @param imageRequestBo 图片请求参数
     * @return 处理结果
     */
    public AjaxResult processAutomaticBox(ImageRequestBo imageRequestBo) {
        try {
            Map<String, Object> params = buildRequestParams(imageRequestBo);
            Optional<JSONObject> responseOpt = doPost(AUTOMATIC_BOX_URL, params);
            return extractResponseData(responseOpt, AUTOMATIC_BOX_URL, "自动框选请求")
                .orElse(AjaxResult.fail("API调用失败"));
        } catch (Exception e) {
            log.error("处理自动框选请求失败", e);
            return AjaxResult.fail("服务异常：" + e.getMessage());
        }
    }

    /**
     * 验证图片请求参数
     */
    private boolean validateImageRequest(ImageRequestBo request) {
        return StringUtils.isNotBlank(request.getImageBase64()) ||
               StringUtils.isNotBlank(request.getImageUrl());
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildRequestParams(ImageRequestBo imageRequestBo) {
        Map<String, Object> bodyParams = new HashMap<>();

        // 优先使用base64，其次使用URL
        if (StringUtils.isNotBlank(imageRequestBo.getImageBase64())) {
            bodyParams.put("image_base64", imageRequestBo.getImageBase64());
        } else if (StringUtils.isNotBlank(imageRequestBo.getImageUrl())) {
            bodyParams.put("image_url", imageRequestBo.getImageUrl());
        }

        // 返回题目类型
        bodyParams.put("type", 1);

        return bodyParams;
    }

    /**
     * 通用的好未来API调用方法
     * 支持扩展其他好未来API接口
     *
     * @param url API地址
     * @param params 请求参数
     * @return 处理结果
     */
    public AjaxResult callHwlApi(String url, Map<String, Object> params) {
        String requestInfo = "API request: " + url;
        return extractResponseData(doPost(url, params), url, requestInfo)
            .orElse(AjaxResult.fail("API调用失败"));
    }

    /**
     * 对象转Map的通用方法
     * 使用Optional优化空值处理
     */
    private Map<String, Object> convertToMap(Object requestBody) {
        return Optional.ofNullable(requestBody)
            .map(body -> {
                // 简单的对象转Map实现，可根据需要扩展
                if (body instanceof Map) {
                    return (Map<String, Object>) body;
                }
                // 这里可以添加更复杂的转换逻辑
                return new HashMap<String, Object>();
            })
            .orElse(new HashMap<>());
    }

    /**
     * 发送POST请求（支持对象参数）
     * 注意：requestBody不支持json注解，只支持原生的变量名称转换
     */
    public AjaxResult callHwlApiWithObject(String url, Object requestBody) {
        return callHwlApi(url, convertToMap(requestBody));
    }
}
