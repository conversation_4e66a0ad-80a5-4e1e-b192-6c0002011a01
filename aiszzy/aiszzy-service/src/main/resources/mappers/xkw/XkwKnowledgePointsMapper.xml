<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.xkw.xkwKnowledgePoints.mapper.XkwKnowledgePointsMapper">

	<select id="getXkwKnowledgePointsListByCondition"
			resultType="com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo">
		select p.* from xkw_knowledge_points p inner join xkw_courses c on p.course_id = c.id
		<where>
			<if test="stageId != null" >
				and c.stage_id = #{stageId}
			</if>
			<if test="subject != null and subject != ''">
				and c.subject_id = #{subject}
			</if>
		</where>
		order by p.ordinal asc
	</select>
</mapper>