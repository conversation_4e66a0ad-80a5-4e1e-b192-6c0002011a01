package com.light.contants;

import com.light.enums.ThirdSourceTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class AISzzyConstants {


    public static final Integer THIRD_USER_SOURCE_INTEFACE = 1;
    //学科网Appcode
    public static final String THIRD_USER_XKW_APP_CODE = ThirdSourceTypeEnum.XKW.getCode();
    // 安卓设备软件激活码位数
    public static final Integer ACTIVATION_CODE_LENGTH = 6;

    public enum PaperPageSize {
        A4(794, 1122),
        A3(1588, 1122);

        private Integer width;
        private Integer height;

        PaperPageSize(Integer width, Integer height) {
            this.width = width;
            this.height = height;
        }

        public Integer getWidth() {
            return width;
        }

        public Integer getHeight() {
            return height;
        }

    }

    public enum PaperRectInfo {
        QR_CODE(154, 140, 166, 166),
        USER_ID(548, 140, 324, 56),
        USER_NAME(1040, 140, 256, 56),
        A4_LEFT_TOP(90, 116, 28, 56),
        A4_RIGHT_TOP(1532, 116, 28, 56),
        A4_LEFT_BOTTOM(90, 2192, 28, 56),
        A4_RIGHT_BOTTOM(1532, 2192, 28, 56);

        private Integer x;
        private Integer y;
        private Integer w;
        private Integer h;

        PaperRectInfo(Integer x, Integer y, Integer w, Integer h) {
            this.x = x;
            this.y = y;
            this.w = w;
            this.h = h;
        }

        public Integer getX() {
            return x;
        }

        public Integer getY() {
            return y;
        }

        public Integer getW() {
            return w;
        }

        public Integer getH() {
            return h;
        }
    }

    public enum QuestionShowType {
        IMAGE(0L, "图片"),

        HTML(1L, "html");
        private Long type;
        private String value;

        QuestionShowType(Long type, String value) {
            this.type = type;
            this.value = value;
        }

        public Long getType() {
            return this.type;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum Term {
        LAST(1, "上学期"),

        NEXT(2, "下学期");
        private Integer semester;
        private String value;

        Term(Integer semester, String value) {
            this.semester = semester;
            this.value = value;
        }

        public Integer getSemester() {
            return this.semester;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum InsideSourceType {
        RESOURCE_QUESTION("resource_question"),
        SCHOOL_RESOURCE_QUESTION("school_resource_question");
        private String type;

        InsideSourceType(String type) {
            this.type = type;
        }

        public String getType() {
            return this.type;
        }

    }

    /**
     * 生成规则类型 0普通作业，1分层作业，2教辅关联作业，3靶向作业
     */
    public enum HomeWorkGenerateRuleType {
        ORDINARY(0, "普通作业"),
        LAYERED(1, "分层作业"),
        PRACTICEBOOK(2, "教辅关联作业"),
        TARGET(3, "靶向作业");

        HomeWorkGenerateRuleType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }

        private Integer code;
        private String value;

        public Integer getCode() {
            return this.code;
        }

        public String getValue() {
            return this.value;
        }

    }

    /**
     * 来源（1、自建；2、引用教辅；3引用作业本）
     */
    public enum HomeWorkSourceType {
        ORDINARY(1, "普通作业"),
        PRACTICEBOOK(2, "引用教辅"),
        HOMEWORKBOOK(3, "引用作业本");

        HomeWorkSourceType(Integer code, String value) {
            this.code = code;
            this.value = value;
        }


        private Integer code;
        private String value;

        public Integer getCode() {
            return this.code;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 获取教辅文件类型
     *
     * @param extName the extname 文件类型字符串
     * @return {@link Integer }
     */
    public static Integer getPracticeBookFileValByType(String extName) {
        if (extName.equalsIgnoreCase("zip")) {
            return 2;
        }
        if (extName.equalsIgnoreCase("pdf")) {
            return 1;
        }
        return null;
    }


    /**
     * 获取学科网学段
     *
     * @param gradeId 年级 ID
     * @return {@link Integer }
     */
    public static Integer getXkwStageByGrade(Integer gradeId) {
        if (gradeId < 7) {
            return 2;
        }
        if (gradeId > 9) {
            return 4;
        }
        return 3;
    }

    /**
     * 获取学科网学段
     *
     * @param stage 学段
     * @return {@link Integer }
     */
    public static List<Integer> getXkwGradeListByStage(Integer stage) {
        if (stage == 2) {
            return IntStream.range(1, 7).boxed().collect(Collectors.toList());
        }
        if (stage == 3) {
            return IntStream.range(7, 10).boxed().collect(Collectors.toList());
        }
        if (stage == 4) {
            return IntStream.range(10, 13).boxed().collect(Collectors.toList());
        }
        return null;
    }


    /**
     * 获取学科网学科
     *
     * @param subject the subject 学科
     * @return {@link String }
     */
    public static String getXkwSubject(String subject) {
        if (subject.equals("30")) {
            return "7";
        }
        return subject;
    }
}
