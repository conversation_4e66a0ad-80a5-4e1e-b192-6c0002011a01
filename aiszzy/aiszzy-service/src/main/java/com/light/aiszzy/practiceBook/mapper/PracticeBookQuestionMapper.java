package com.light.aiszzy.practiceBook.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookQuestionDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 教辅题目表，用于存储题目信息Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface PracticeBookQuestionMapper extends BaseMapper<PracticeBookQuestionDto> {

	List<PracticeBookQuestionVo> getPracticeBookQuestionListByCondition(PracticeBookQuestionConditionBo condition);

	Integer selectMaxOrderByPracticeBookPageOid(@Param("practiceBookPageOid") String practiceBookPageOid);

    boolean batchUpdateByOid(@Param("questionList") List<PracticeBookQuestionDto> questionDtoList);
}
