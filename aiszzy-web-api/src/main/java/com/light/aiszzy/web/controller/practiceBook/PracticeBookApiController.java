package com.light.aiszzy.web.controller.practiceBook;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.enums.PracticeBookStatus;
import com.light.enums.SchoolBookStatus;
import com.light.security.service.CurrentUserService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.aiszzy.schoolBook.entity.bo.SchoolBookConditionBo;
import com.light.aiszzy.schoolBook.entity.vo.PracticeBookWithSchoolVo;
import com.light.aiszzy.schoolBook.service.SchoolBookApiService;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.service.PracticeBookApiService;
import com.light.aiszzy.practiceBook.service.PracticeBookCatalogApiService;
import com.light.aiszzy.practiceBook.service.PracticeBookQuestionApiService;
import com.light.contants.ConstantsInteger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 教辅信息表(学校已开通教辅)
 *
 * <AUTHOR>
 * @date 2025/7/16 18:57
 */
@RestController
@Validated
@RequestMapping("/api/practiceBook")
@Api(value = "", tags = "教辅信息表接口（学校已开通教辅）")
public class PracticeBookApiController {

    @Resource
    private SchoolBookApiService schoolBookApiService;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private PracticeBookApiService practiceBookApiService;

    @Resource
    private PracticeBookCatalogApiService practiceBookCatalogApiService;

    @Resource
    private PracticeBookQuestionApiService practiceBookQuestionApiService;

    /**
     * 查询当前学校已开通的教辅列表
     *
     * @param condition 查询条件
     * @return 当前学校已开通的教辅分页列表
     */
    @PostMapping("/school-opened-list")
    @ApiOperation(value = "查询当前学校已开通的教辅列表", httpMethod = "POST")
    public AjaxResult<PageInfo<PracticeBookWithSchoolVo>> getSchoolOpenedPracticeBookList(
            @Validated @RequestBody SchoolBookConditionBo condition) {

        // 获取当前登录用户信息
        LoginAccountVo currentUser = currentUserService.getCurrentUser();
        if (currentUser == null || currentUser.getCurrentUser() == null
            || currentUser.getCurrentUser().getUserOrg() == null
            || currentUser.getCurrentUser().getUserOrg().getCode() == null) {
            return AjaxResult.fail("获取当前用户学校信息失败");
        }

        // 设置当前用户的学校CODE
        String orgCode = currentUser.getCurrentUser().getUserOrg().getCode();
        condition.setOrgCode(orgCode);

        // 设置默认查询条件：只查询启用的数据
        condition.setStatus(SchoolBookStatus.ENABLED.getCode());
        // 设置默认查询条件：只查询未删除的记录
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // 设置默认查询条件：只查询已上架的数据
        condition.setPracticeBookStatus(PracticeBookStatus.PUBLISHED.getCode());

        // 调用服务查询当前学校已开通的教辅列表
        return schoolBookApiService.getPracticeBookWithSchoolPageListByCondition(condition);
    }

    /**
     * 查询教辅详情
     *
     * @param practiceBookOid 教辅OID
     * @return 教辅详情信息
     */
    @GetMapping("/detail/{practiceBookOid}")
    @ApiOperation(value = "查询教辅详情", httpMethod = "GET")
    public AjaxResult<PracticeBookVo> getPracticeBookDetail(@PathVariable String practiceBookOid) {

        // 验证教辅是否属于当前学校已开通的教辅
        if (!isSchoolOpenedPracticeBook(practiceBookOid)) {
            return AjaxResult.fail("该教辅未开通或无权限访问");
        }

        // 调用服务查询教辅详情
        return practiceBookApiService.getDetail(practiceBookOid);
    }

    /**
     * 查询教辅目录树形结构
     *
     * @param practiceBookOid 教辅OID
     * @return 教辅目录树形结构
     */
    @GetMapping("/catalog/tree/{practiceBookOid}")
    @ApiOperation(value = "查询教辅目录树形结构", httpMethod = "GET")
    public AjaxResult<List<PracticeBookCatalogVo>> getPracticeBookCatalogTree(@PathVariable String practiceBookOid) {

        // 验证教辅是否属于当前学校已开通的教辅
        if (!isSchoolOpenedPracticeBook(practiceBookOid)) {
            return AjaxResult.fail("该教辅未开通或无权限访问");
        }

        // 调用服务查询教辅目录树形结构
        return practiceBookCatalogApiService.queryTreeByPracticeBookOid(practiceBookOid);
    }

    /**
     * 查询教辅目录下的题目列表
     *
     * @param condition 查询条件
     * @return 教辅题目分页列表
     */
    @PostMapping("/question/list")
    @ApiOperation(value = "查询教辅目录下的题目列表", httpMethod = "POST")
    public AjaxResult<PageInfo<PracticeBookQuestionVo>> getPracticeBookQuestionList(
            @Validated @RequestBody PracticeBookQuestionConditionBo condition) {

        // 验证教辅是否属于当前学校已开通的教辅
        String practiceBookOid = condition.getPracticeBookOid();
        if (practiceBookOid == null || !isSchoolOpenedPracticeBook(practiceBookOid)) {
            return AjaxResult.fail("该教辅未开通或无权限访问");
        }

        // 设置默认查询条件：只查询未删除的记录
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

        // 调用服务查询教辅题目列表
        return practiceBookQuestionApiService.getPracticeBookQuestionPageListByCondition(condition);
    }

    /**
     * 验证教辅是否属于当前学校(已开通，上架，未删除)的教辅
     *
     * @param practiceBookOid 教辅OID
     * @return true-已开通，false-未开通或无权限
     */
    private boolean isSchoolOpenedPracticeBook(String practiceBookOid) {
        // 获取当前登录用户信息
        LoginAccountVo currentUser = currentUserService.getCurrentUser();
        if (currentUser == null || currentUser.getCurrentUser() == null
            || currentUser.getCurrentUser().getUserOrg() == null
            || currentUser.getCurrentUser().getUserOrg().getCode() == null) {
            return false;
        }

        // 构建查询条件
        SchoolBookConditionBo condition = new SchoolBookConditionBo();
        condition.setOrgCode(currentUser.getCurrentUser().getUserOrg().getCode());
        condition.setBookOid(practiceBookOid);
        condition.setStatus(SchoolBookStatus.ENABLED.getCode());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setPracticeBookStatus(PracticeBookStatus.PUBLISHED.getCode());
        condition.setPageNo(ConstantsInteger.NUM_1);
        condition.setPageSize(ConstantsInteger.NUM_1);

        // 查询是否存在开通记录
        AjaxResult<PageInfo<PracticeBookWithSchoolVo>> result =
            schoolBookApiService.getPracticeBookWithSchoolPageListByCondition(condition);

        return result.isSuccess() && result.getData() != null
            && result.getData().getList() != null && !result.getData().getList().isEmpty();
    }
}
