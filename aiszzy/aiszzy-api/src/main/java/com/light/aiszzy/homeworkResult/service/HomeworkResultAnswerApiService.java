package com.light.aiszzy.homeworkResult.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homeworkResult.api.HomeworkResultAnswerApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 学生题目答案表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkResultAnswerApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkResultAnswerApiService.HomeworkResultAnswerApiFallbackFactory.class)
@Component
public interface HomeworkResultAnswerApiService  extends HomeworkResultAnswerApi {

	@Component
	class HomeworkResultAnswerApiFallbackFactory implements FallbackFactory<HomeworkResultAnswerApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkResultAnswerApiService.HomeworkResultAnswerApiFallbackFactory.class);
		@Override
		public HomeworkResultAnswerApiService create(Throwable cause) {
			HomeworkResultAnswerApiService.HomeworkResultAnswerApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkResultAnswerApiService() {
				public AjaxResult getHomeworkResultAnswerPageListByCondition(HomeworkResultAnswerConditionBo condition){
					return AjaxResult.fail("学生题目答案表查询失败");
				}
				public AjaxResult getHomeworkResultAnswerListByCondition(HomeworkResultAnswerConditionBo condition){
					return AjaxResult.fail("学生题目答案表查询失败");
				}

				public AjaxResult addHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo){
					return AjaxResult.fail("学生题目答案表新增失败");
				}

				public AjaxResult updateHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo){
					return AjaxResult.fail("学生题目答案表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("学生题目答案表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("学生题目答案表删除失败");
				}
			};
		}
	}
}