package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkBookOptionRecordApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业本映射印送记录接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@FeignClient(contextId = "homeworkBookOptionRecordApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkBookOptionRecordApiService.HomeworkBookOptionRecordApiFallbackFactory.class)
@Component
public interface HomeworkBookOptionRecordApiService  extends HomeworkBookOptionRecordApi {

	@Component
	class HomeworkBookOptionRecordApiFallbackFactory implements FallbackFactory<HomeworkBookOptionRecordApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkBookOptionRecordApiService.HomeworkBookOptionRecordApiFallbackFactory.class);
		@Override
		public HomeworkBookOptionRecordApiService create(Throwable cause) {
			HomeworkBookOptionRecordApiService.HomeworkBookOptionRecordApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkBookOptionRecordApiService() {
				public AjaxResult getHomeworkBookOptionRecordPageListByCondition(HomeworkBookOptionRecordConditionBo condition){
					return AjaxResult.fail("作业本映射印送记录查询失败");
				}
				public AjaxResult getHomeworkBookOptionRecordListByCondition(HomeworkBookOptionRecordConditionBo condition){
					return AjaxResult.fail("作业本映射印送记录查询失败");
				}

				public AjaxResult addHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo){
					return AjaxResult.fail("作业本映射印送记录新增失败");
				}

				public AjaxResult updateHomeworkBookOptionRecord(HomeworkBookOptionRecordBo homeworkBookOptionRecordBo){
					return AjaxResult.fail("作业本映射印送记录更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业本映射印送记录获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业本映射印送记录删除失败");
				}
			};
		}
	}
}