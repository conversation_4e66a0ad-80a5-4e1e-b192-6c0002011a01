package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-12 14:16:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework")
public class HomeworkDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
	 */
	@TableField("code")
	private String code;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 作业名称
	 */
	@TableField("homework_name")
	private String homeworkName;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@TableField("term")
	private Integer term;

	/**
	 * 扫描状态 0：未扫描  1：已扫描(这条记录是否被使用过)
	 */
	@TableField("is_scan")
	private Integer isScan;

	/**
	 * 报告状态：0：未生成，1：生成
	 */
	@TableField("report_state")
	private Integer reportState;

	/**
	 * 校本作业pdf地址
	 */
	@TableField("homework_pdf_url")
	private String homeworkPdfUrl;

	/**
	 * 作业答案解析地址
	 */
	@TableField("homework_answer_pdf_url")
	private String homeworkAnswerPdfUrl;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 是否使用 0为被作业本绑定 1已经绑定作业本
	 */
	@TableField("is_use")
	private Integer isUse;

	/**
	 * 作业本oid
	 */
	@TableField("homework_book_oid")
	private String homeworkBookOid;

	/**
	 * 制卡页面信息
	 */
	@TableField("paper_json")
	private String paperJson;

	/**
	 * 二维码坐标位置
	 */
	@TableField("qr_code_rect")
	private String qrCodeRect;

	/**
	 * 用户学号坐标位置
	 */
	@TableField("user_id_rect")
	private String userIdRect;

	/**
	 * 用户名字坐标位置
	 */
	@TableField("user_name_rect")
	private String userNameRect;

	/**
	 * 题目数
	 */
	@TableField("question_num")
	private Long questionNum;

	/**
	 * 页个数
	 */
	@TableField("page_num")
	private Integer pageNum;

	/**
	 * 生成文件的纸张大小(0:A3 1:A4 2:8K 3:16K)
	 */
	@TableField("page_size")
	private Integer pageSize;

	/**
	 * 作业是否审核0未审核，1已审核
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 生成规则类型 0普通作业，1分层作业，2教辅关联作业，3靶向作业
	 */
	@TableField("generate_rule_type")
	private Integer generateRuleType;

	/**
	 * 来源（1、自建；2、引用教辅；3引用作业本）
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 教辅的目录oid或作业本对应作业的oid
	 */
	@TableField("source_oid")
	private String sourceOid;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
