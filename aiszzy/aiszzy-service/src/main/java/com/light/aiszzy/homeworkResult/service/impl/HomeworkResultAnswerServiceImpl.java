package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultAnswerService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultAnswerMapper;
import com.light.core.entity.AjaxResult;
/**
 * 学生题目答案表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkResultAnswerServiceImpl extends ServiceImpl<HomeworkResultAnswerMapper, HomeworkResultAnswerDto> implements IHomeworkResultAnswerService {

	@Resource
	private HomeworkResultAnswerMapper homeworkResultAnswerMapper;
	
    @Override
	public List<HomeworkResultAnswerVo> getHomeworkResultAnswerListByCondition(HomeworkResultAnswerConditionBo condition) {
        return homeworkResultAnswerMapper.getHomeworkResultAnswerListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo) {
		HomeworkResultAnswerDto homeworkResultAnswer = new HomeworkResultAnswerDto();
		BeanUtils.copyProperties(homeworkResultAnswerBo, homeworkResultAnswer);
		homeworkResultAnswer.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkResultAnswer.setOid(IdUtil.simpleUUID());
		if(save(homeworkResultAnswer)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkResultAnswer(HomeworkResultAnswerBo homeworkResultAnswerBo) {
		LambdaQueryWrapper<HomeworkResultAnswerDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultAnswerDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultAnswerDto::getOid, homeworkResultAnswerBo.getOid());
		HomeworkResultAnswerDto homeworkResultAnswer = getOne(lqw);
		Long id = homeworkResultAnswer.getId();
		if(homeworkResultAnswer == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkResultAnswerBo, homeworkResultAnswer);
		homeworkResultAnswer.setId(id);
		if(updateById(homeworkResultAnswer)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkResultAnswerVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkResultAnswerDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultAnswerDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultAnswerDto::getOid, oid);
		HomeworkResultAnswerDto homeworkResultAnswer = getOne(lqw);
	    HomeworkResultAnswerVo homeworkResultAnswerVo = new HomeworkResultAnswerVo();
		if(homeworkResultAnswer != null){
			BeanUtils.copyProperties(homeworkResultAnswer, homeworkResultAnswerVo);
		}
		return homeworkResultAnswerVo;
	}

}