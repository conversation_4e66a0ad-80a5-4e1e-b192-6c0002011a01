package com.light.aiszzy.web.controller.cos;

import com.light.core.entity.AjaxResult;
import com.light.core.file.tencent.TencentCOSProperties;
import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@RefreshScope
@RestController
@RequestMapping("/tencent/cos")
public class TencentCOSController {

    @Resource
    private TencentCOSProperties tencentCOSProperties;

    @GetMapping("getCredential")
    public AjaxResult getCredential() {
        TreeMap<String, Object> config = new TreeMap<>();
        try {
            int expiredSeconds = 1800;
            // 云 api 密钥 SecretId
            config.put("secretId", tencentCOSProperties.getAccessKey());
            // 云 api 密钥 SecretKey
            config.put("secretKey", tencentCOSProperties.getAccessSecret());
            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", expiredSeconds);
            // 换成你的 bucket
            config.put("bucket", tencentCOSProperties.getBucketName());
            // 换成 bucket 所在地区
            config.put("region", tencentCOSProperties.getRegionName());
            // 可以通过 allowPrefixes 指定前缀数组, 例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
            config.put("allowPrefixes", new String[] {"/*"});
            // 密钥的权限列表。简单上传和分片需要以下的权限，其他权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                    // 简单上传
                    "name/cos:PutObject",
                    "name/cos:PostObject",
                    // 分片上传
                    "name/cos:InitiateMultipartUpload",
                    "name/cos:ListMultipartUploads",
                    "name/cos:ListParts",
                    "name/cos:UploadPart",
                    "name/cos:CompleteMultipartUpload"
            };
            config.put("allowActions", allowActions);
            Response response = CosStsClient.getCredential(config);
            Map<String, Object> result = new HashMap<>();
            result.put("tmpSecretId", response.credentials.tmpSecretId);
            result.put("tmpSecretKey", response.credentials.tmpSecretKey);
            result.put("sessionToken", response.credentials.sessionToken);
            result.put("expiredSeconds", expiredSeconds);
            return AjaxResult.success(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.fail("获取失败");

    }
}
