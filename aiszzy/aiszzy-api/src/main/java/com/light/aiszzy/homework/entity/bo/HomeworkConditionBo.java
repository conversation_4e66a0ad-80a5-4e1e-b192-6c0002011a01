package com.light.aiszzy.homework.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 作业表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-12 14:16:46
 */
@Data
public class HomeworkConditionBo extends PageLimitBo{

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	private String homeworkCode;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;

	/**
	 * 作业名称
	 */
	@ApiModelProperty("作业名称")
	private String homeworkName;

	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@ApiModelProperty("学期  1:上学期  2：下学期")
	private Long term;

	/**
	 * 扫描状态 0：未扫描  1：已扫描(这条记录是否被使用过)
	 */
	@ApiModelProperty("扫描状态 0：未扫描  1：已扫描(这条记录是否被使用过)")
	private Integer isScan;

	/**
	 * 题目数数量(该作业题目数)
	 */
	@ApiModelProperty("题目数数量(该作业题目数)")
	private Integer topicNum;

	/**
	 * 报告状态：0：未生成，1：生成
	 */
	@ApiModelProperty("报告状态：0：未生成，1：生成")
	private Integer reportState;

	/**
	 * 校本作业pdf地址
	 */
	@ApiModelProperty("校本作业pdf地址")
	private String homeworkPdfUrl;

	/**
	 * 作业答案解析地址
	 */
	@ApiModelProperty("作业答案解析地址")
	private String homeworkAnswerPdfUrl;

	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private String year;

	/**
	 * 是否使用 0为被作业本绑定 1已经绑定作业本
	 */
	@ApiModelProperty("是否使用 0为被作业本绑定 1已经绑定作业本")
	private Integer isUse;

	/**
	 * 作业本oid
	 */
	@ApiModelProperty("作业本oid")
	private String homeworkBookOid;

	/**
	 * 制卡页面信息
	 */
	@ApiModelProperty("制卡页面信息")
	private String paperJson;

	/**
	 * 二维码坐标位置
	 */
	@ApiModelProperty("二维码坐标位置")
	private String qrCodeRect;

	/**
	 * 用户学号坐标位置
	 */
	@ApiModelProperty("用户学号坐标位置")
	private String userIdRect;

	/**
	 * 用户名字坐标位置
	 */
	@ApiModelProperty("用户名字坐标位置")
	private String userNameRect;

	/**
	 * 题目数
	 */
	@ApiModelProperty("题目数")
	private Long questionNum;

	/**
	 * 页个数
	 */
	@ApiModelProperty("页个数")
	private Integer pageNum;

	/**
	 * 生成文件的纸张大小(0:A3 1:A4 2:8K 3:16K)
	 */
	@ApiModelProperty("生成文件的纸张大小(0:A3 1:A4 2:8K 3:16K)")
	private Integer pageSize;

	/**
	 * 作业是否审核0未审核，1已审核
	 */
	@ApiModelProperty("作业是否审核0未审核，1已审核")
	private Integer status;

	/**
	 * 生成规则类型 0普通作业，1分层作业，2教辅关联作业，3靶向作业
	 */
	@ApiModelProperty("生成规则类型 0普通作业，1分层作业，2教辅关联作业，3靶向作业")
	private Integer generateRuleType;

	/**
	 * 来源（1、自建；2、引用教辅；3引用作业本）
	 */
	@ApiModelProperty("来源（1、自建；2、引用教辅；3引用作业本）")
	private Integer sourceType;

	/**
	 * 教辅的目录oid或作业本对应作业的oid
	 */
	@ApiModelProperty("教辅的目录oid或作业本对应作业的oid")
	private String sourceOid;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	private String hasBind;

	private String homeBookOid;

	private String homeworkBookCatalogOid;

}
