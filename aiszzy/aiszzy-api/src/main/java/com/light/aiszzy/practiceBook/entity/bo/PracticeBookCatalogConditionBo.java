package com.light.aiszzy.practiceBook.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 教辅目录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class PracticeBookCatalogConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 父目录OID，表示层级关系，NULL表示根目录
	 */
	@ApiModelProperty("父目录OID，表示层级关系，NULL表示根目录")
	private String parentOid;

	/**
	 * 祖籍 OID 集合 多个逗号分割
	 */
	@ApiModelProperty("祖籍 OID 集合 多个逗号分割")
	private String superiorsOids;

	/**
	 * 目录名称
	 */
	@ApiModelProperty("目录名称")
	private String name;

	@ApiModelProperty("目录名称")
	private Integer level;

	/**
	 * 关联的教辅OID
	 */
	@ApiModelProperty("关联的教辅OID")
	private String practiceBookOid;

	/**
	 * 起始页
	 */
	@ApiModelProperty("起始页")
	private Long pageStart;

	/**
	 * 结束页
	 */
	@ApiModelProperty("结束页")
	private Long pageEnd;

	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Integer orderNum;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;


	/**
	 * 页码集合
	 */
	private List<Long> pageNoList;

}
