package com.light.aiszzy.question.entity.vo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题目表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
public class QuestionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 题型id
     */
    private String questionTypeId;

    /**
     * 题型名称
     */
    private String questionTypeName;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 年份
     */
    private String year;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 难度
     */
    private Long difficultId;

    /**
     * 题目url或文字
     */
    private String quesBody;

    /**
     * 公共题干url或文字
     */
    private String publicQues;

    /**
     * 答案url或文字
     */
    private String quesAnswer;

    /**
     * 解析url或文字
     */
    private String analysisAnswer;

    /**
     * 题目展示类型  0：图片url  1：html文字 
     */
    private Long quesBodyType;

    /**
     * 公共题干展示类型  0：图片url  1：html文字
     */
    private Long publicQuesType;

    /**
     * 答案展示类型  0：图片url  1：html文字 
     */
    private Long quesAnswerType;

    /**
     * 解析展示类型  0：图片url  1：html文字 
     */
    private Long analysisAnswerType;

    /**
     * 来源 学科网xkw，好未来hwl
     */
    private String thirdSourceType;

    /**
     * 外部id
     */
    private String thirdOutId;

    /**
     * 知识点
     */
    private String knowledgePointsId;

    /**
     * 章节ID
     */
    private String chapterId;

    /**
     * 节ID
     */
    private String sectionId;

    /**
     * 内部来源，resource_question,school_resource_question
     */
    private String insideSourceType;

    /**
     * 内部来源对应oid
     */
    private String insideLinkOid;

    /**
     * 相似题json，解析后question_oid存入json
     */
    private String similarRecommendResult;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;


    /**
     * 相似题集合， similarRecommendResult json array 转换
     */
    private List<QuestionVo> similarRecommendResultList;


    /**
     *  获取相似题集合
     * @return {@link List }<{@link QuestionVo }>
     */
    public List<QuestionVo> fetSimilarRecommendResultList() {
        if(StrUtil.isNotEmpty(this.similarRecommendResult)) {
            return JSON.parseArray(this.similarRecommendResult, QuestionVo.class);
        }
        return similarRecommendResultList;
    }
}
