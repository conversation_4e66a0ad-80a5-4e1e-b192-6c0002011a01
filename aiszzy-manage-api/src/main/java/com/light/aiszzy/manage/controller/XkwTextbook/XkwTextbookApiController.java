package com.light.aiszzy.manage.controller.XkwTextbook;


import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.service.HomeworkBookApiService;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.bo.XkwTextbookVersionsConditionBo;
import com.light.aiszzy.xkw.xkwTextbookVersions.entity.vo.XkwTextbookVersionsVo;
import com.light.aiszzy.xkw.xkwTextbookVersions.service.XkwTextbookVersionsApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教材
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@RestController
@Validated
@Api(value = "", tags = "教材接口" )
public class XkwTextbookApiController {

	@Autowired
	private HomeworkBookApiService homeworkBookApiService;

	@Autowired
	private XkwTextbookVersionsApiService xkwTextbookVersionsApiService;

	@PostMapping("/xkwTextbookVersions/pageList")
	@ApiOperation(value = "分页查询教材版本",httpMethod = "POST")
	public AjaxResult<PageInfo<XkwTextbookVersionsVo>> getXkwTextbookVersionsPageListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition){
		return xkwTextbookVersionsApiService.getXkwTextbookVersionsPageListByCondition(condition);
	}

	@PostMapping("/xkwTextbookVersions/list")
	@ApiOperation(value = "查询所有教材版本",httpMethod = "POST")
	public AjaxResult<List<XkwTextbookVersionsVo>> getXkwTextbookVersionsAllListByCondition(@RequestBody XkwTextbookVersionsConditionBo condition){
		return xkwTextbookVersionsApiService.getXkwTextbookVersionsListByCondition(condition);
	}
	@GetMapping("/xkw/textbookVersions")
	@OperationLogAnnotation(moduleName = "教材版本", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult textbookVersions(@RequestParam("grade") String grade, @RequestParam("subject") String subject) {
		return homeworkBookApiService.textbookVersions(grade, subject);
	}
}
