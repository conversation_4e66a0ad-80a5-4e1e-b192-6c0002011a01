package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookDto;
import com.light.aiszzy.practiceBook.service.impl.PracticeBookServiceImpl;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.question.mapper.QuestionMapper;
import com.light.aiszzy.resourcesUserAddToCart.entity.dto.ResourcesUserAddToCartDto;
import com.light.aiszzy.resourcesUserAddToCart.service.impl.ResourcesUserAddToCartServiceImpl;
import com.light.contants.AISzzyConstants;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.service.IHomeworkService;
import com.light.aiszzy.homework.mapper.HomeworkMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作业表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class HomeworkServiceImpl extends ServiceImpl<HomeworkMapper, HomeworkDto> implements IHomeworkService {

    @Resource
    private HomeworkMapper homeworkMapper;

    @Resource
    private HomeworkQuestionServiceImpl homeworkQuestionService;

    @Resource
    private PracticeBookServiceImpl practiceBookService;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private HomeworkPageServiceImpl homeworkPageService;

    @Resource
    private ResourcesUserAddToCartServiceImpl cartServiceImpl;

    @Override
    public List<HomeworkVo> getHomeworkListByCondition(HomeworkConditionBo condition) {
        return homeworkMapper.getHomeworkListByCondition(condition);
    }

    @Override
    public AjaxResult listBindAndNoBind(HomeworkConditionBo condition) {
        Map<String, Object> result = new HashMap<String, Object>(4);
        HomeworkConditionBo noBindCondition = new HomeworkConditionBo();
        BeanUtils.copyProperties(condition, noBindCondition);
        noBindCondition.setHasBind("no");
        noBindCondition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<HomeworkVo> noBindList = getHomeworkListByCondition(noBindCondition);
        HomeworkConditionBo bindCondition = new HomeworkConditionBo();
        BeanUtils.copyProperties(condition, bindCondition);
        bindCondition.setHasBind("yes");
        bindCondition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<HomeworkVo> bindList = getHomeworkListByCondition(bindCondition);
        result.put("noBindHomeworkList", noBindList);
        result.put("bindHomeworkList", bindList);
        return AjaxResult.success(result);
    }

    @Override
    public String generateCode() {
        final int maxRetries = 10;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            String code = RandomUtil.randomString(8);
            LambdaQueryWrapper<HomeworkDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqw.eq(HomeworkDto::getCode, code);
            if(CollectionUtil.isEmpty(list(lqw))){
                return code;
            }
        }
        return null;
    }

    @Override
    public AjaxResult addHomework(HomeworkBo homeworkBo) {
        HomeworkDto homework = new HomeworkDto();
        BeanUtils.copyProperties(homeworkBo, homework);
        homework.setIsDelete(StatusEnum.NOTDELETE.getCode());
        homework.setOid(IdUtil.simpleUUID());
        homework.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.LAYERED.getCode());
        homework.setSourceType(AISzzyConstants.HomeWorkSourceType.ORDINARY.getCode());
        if (save(homework)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateHomework(HomeworkBo homeworkBo) {
        LambdaQueryWrapper<HomeworkDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkDto::getOid, homeworkBo.getOid());
        HomeworkDto homework = getOne(lqw);
        Long id = homework.getId();
        if (homework == null) {
            return AjaxResult.fail("保存失败");
        }

        BeanUtils.copyProperties(homeworkBo, homework);
        homework.setId(id);

        if(StringUtils.isEmpty(homework.getCode())){
            String code = generateCode();
            if(StringUtils.isEmpty(code)){
                return AjaxResult.fail("生成唯一code失败,保存失败");
            }
            homework.setCode(code);
        }

        if (updateById(homework)) {
            if (StringUtils.isNotBlank(homeworkBo.getPaperJson())) {
                List<HomeworkPageDto> list = homeworkPageService.list(new LambdaQueryWrapper<HomeworkPageDto>()
                        .eq(HomeworkPageDto::getHomeworkOid, homework.getOid())
                        .eq(HomeworkPageDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
                Map<Integer, HomeworkPageDto> pageMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(list)) {
                    pageMap = list.stream().collect(Collectors.toMap(p1 -> p1.getPageNo(), p2 -> p2, (p1, p2) -> p1));
                }
                JSONArray jsonArr = JSONUtil.parseArray(homeworkBo.getPaperJson());

                List<HomeworkPageDto> arr = new ArrayList<>();
                for (int i = 0; i < jsonArr.size(); i++) {
                    JSONObject jsonObject = jsonArr.getJSONObject(i);
                    Integer number = jsonObject.getInt("number");
                    String questionList = jsonObject.getStr("questionList");

                    if (pageMap.containsKey(number)) {
                        HomeworkPageDto homeworkPageDto = pageMap.get(number);
                        homeworkPageDto.setQuestionJson(questionList);
                        arr.add(homeworkPageDto);
                        list.remove(homeworkPageDto);
                    } else {
                        HomeworkPageDto homeworkPageDto = new HomeworkPageDto();
                        homeworkPageDto.setOid(IdUtil.simpleUUID());
                        homeworkPageDto.setPageNo(number);
                        homeworkPageDto.setQuestionJson(questionList);
                        arr.add(homeworkPageDto);
                    }
                }

                if (CollectionUtil.isNotEmpty(arr)) {
                    homeworkPageService.saveOrUpdateBatch(arr);
                }
                if (CollectionUtil.isNotEmpty(list)) {
                    list.forEach(obj->obj.setIsDelete(StatusEnum.ISDELETE.getCode()));
                    homeworkPageService.updateBatchById(list);
                }
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult<HomeworkVo> cartHomework(String oid) {
        HomeworkDto newDto = new HomeworkDto();
        newDto.setOid(IdUtil.simpleUUID());
        save(newDto);

        List<ResourcesUserAddToCartDto> cartQuestionList = cartServiceImpl.list(new LambdaQueryWrapper<ResourcesUserAddToCartDto>()
                .eq(ResourcesUserAddToCartDto::getOid, oid)
                .eq(ResourcesUserAddToCartDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .orderByAsc(ResourcesUserAddToCartDto::getId));
        Map<String, QuestionDto> quesMap = new HashMap<>();
        List<HomeworkQuestionDto> copyQuestionList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(cartQuestionList)) {
            List<String> collect = cartQuestionList.stream().map(ResourcesUserAddToCartDto::getQuestionOid).collect(Collectors.toList());
            List<QuestionDto> questionDtos = questionMapper.selectList(new LambdaQueryWrapper<QuestionDto>()
                    .in(QuestionDto::getOid, collect)
                    .eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
            if (CollectionUtil.isNotEmpty(cartQuestionList)) {
                quesMap = questionDtos.stream().collect((Collectors.toMap(p1 -> p1.getOid(), p2 -> p2, (p1, p2) -> p1)));
            }
            for (ResourcesUserAddToCartDto questionDto : cartQuestionList) {
                if(quesMap.containsKey(questionDto.getQuestionOid())) {
                    QuestionDto queDto = quesMap.get(questionDto.getQuestionOid()); HomeworkQuestionDto homeworkQuestionDto = new HomeworkQuestionDto();
                    HomeworkQuestionDto dto = new HomeworkQuestionDto();
                    BeanUtils.copyProperties(queDto, dto);
                    homeworkQuestionDto.setOid(IdUtil.simpleUUID());
                    homeworkQuestionDto.setId(null);
                    homeworkQuestionDto.setCreateBy(null);
                    homeworkQuestionDto.setUpdateBy(null);
                    homeworkQuestionDto.setCreateTime(null);
                    homeworkQuestionDto.setUpdateTime(null);
                    copyQuestionList.add(homeworkQuestionDto);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(copyQuestionList)) {
            homeworkQuestionService.saveBatch(copyQuestionList);
        }
        HomeworkVo homeworkVo = new HomeworkVo();
        BeanUtils.copyProperties(newDto, homeworkVo);
        return AjaxResult.success(homeworkVo);
    }

    @Override
    public AjaxResult<HomeworkVo> copyHomework(String oid) {
        LambdaQueryWrapper<HomeworkDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkDto::getOid, oid);
        HomeworkDto homework = getOne(lqw);
        if (homework == null) {
            return AjaxResult.fail("复制作业失败");
        }
        HomeworkDto newDto = new HomeworkDto();
        BeanUtils.copyProperties(homework, homework);
        newDto.setOid(IdUtil.simpleUUID());
        newDto.setId(null);
        newDto.setCreateBy(null);
        newDto.setUpdateBy(null);
        newDto.setCreateTime(null);
        newDto.setUpdateTime(null);
        save(newDto);
        List<HomeworkQuestionDto> homeworkQuestionList = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestionDto>()
                .eq(HomeworkQuestionDto::getHomeworkOid, homework.getOid())
                .eq(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .orderByAsc(HomeworkQuestionDto::getQuesOrderNum));
        List<HomeworkQuestionDto> copyQuestionList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(homeworkQuestionList)) {
            for (HomeworkQuestionDto homeworkQuestion : homeworkQuestionList) {
                HomeworkQuestionDto homeworkQuestionDto = new HomeworkQuestionDto();
                BeanUtils.copyProperties(homeworkQuestion, homeworkQuestionDto);
                homeworkQuestionDto.setOid(IdUtil.simpleUUID());
                homeworkQuestionDto.setId(null);
                homeworkQuestionDto.setCreateBy(null);
                homeworkQuestionDto.setUpdateBy(null);
                homeworkQuestionDto.setCreateTime(null);
                homeworkQuestionDto.setUpdateTime(null);
                copyQuestionList.add(homeworkQuestionDto);
            }
        }
        if (CollectionUtil.isNotEmpty(copyQuestionList)) {
            homeworkQuestionService.saveBatch(copyQuestionList);
        }

        HomeworkVo homeworkVo = new HomeworkVo();
        BeanUtils.copyProperties(homework, homeworkVo);
        return AjaxResult.success(homeworkVo);
    }

    @Override
    public AjaxResult<HomeworkVo> originalHomework(HomeworkBo homeworkBo) {
        PracticeBookDto one = practiceBookService.getOne(new LambdaQueryWrapper<PracticeBookDto>()
                .eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(PracticeBookDto::getOid, homeworkBo.getPracticeBookOid()));

        if (one == null) {
            return AjaxResult.fail("获取教辅失败");
        }

        HomeworkDto homework = new HomeworkDto();
        homework.setOid(IdUtil.simpleUUID());
        homework.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.LAYERED.getCode());
        homework.setSourceType(AISzzyConstants.HomeWorkSourceType.PRACTICEBOOK.getCode());
        homework.setSourceOid(homeworkBo.getPracticeBookCatalogOid());
        save(homework);

        List<QuestionVo> questionList = questionMapper.getQuestionListByPracticeBookOid(homeworkBo.getPracticeBookOid(), homeworkBo.getPracticeBookCatalogOid());

        if (CollectionUtil.isNotEmpty(questionList)) {
            List<HomeworkQuestionDto> homeworkQuestionList = new ArrayList<>();
            for (QuestionVo questionVo : questionList) {
                HomeworkQuestionDto dto = new HomeworkQuestionDto();
                BeanUtils.copyProperties(questionVo, dto);
                dto.setOid(IdUtil.simpleUUID());
                dto.setHomeworkOid(homework.getOid());
                dto.setQuestionOid(questionVo.getOid());
                dto.setId(null);
                dto.setCreateBy(null);
                dto.setUpdateBy(null);
                dto.setCreateTime(null);
                dto.setUpdateTime(null);
                homeworkQuestionList.add(dto);
            }
            if (CollectionUtil.isNotEmpty(homeworkQuestionList)) {
                homeworkQuestionService.saveBatch(homeworkQuestionList);
            }
        }
        HomeworkVo homeworkVo = new HomeworkVo();
        BeanUtils.copyProperties(homework, homeworkVo);
        return AjaxResult.success(homeworkVo);
    }

    @Override
    @Transactional
    public AjaxResult<HomeworkVo> layeredHomework(HomeworkBo homeworkBo) {
        PracticeBookDto one = practiceBookService.getOne(new LambdaQueryWrapper<PracticeBookDto>()
                .eq(PracticeBookDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(PracticeBookDto::getOid, homeworkBo.getPracticeBookOid()));

        if (one == null) {
            return AjaxResult.fail("获取教辅失败");
        }
        HomeworkDto homework = new HomeworkDto();
        homework.setOid(IdUtil.simpleUUID());
        homework.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.LAYERED.getCode());
        homework.setSourceType(AISzzyConstants.HomeWorkSourceType.ORDINARY.getCode());
        save(homework);

        List<String> questionSimilarList = questionMapper.getQuestionSimilarListByPracticeBookOid(homeworkBo.getPracticeBookOid(), homeworkBo.getPracticeBookCatalogOid());

        if (CollectionUtil.isNotEmpty(questionSimilarList)) {
            List<QuestionVo> collect = questionSimilarList.stream()
                    .map(o ->
                            JSONUtil.toList(o, QuestionVo.class).stream()
                                    .filter(obj -> obj.getDifficultId().equals(homeworkBo.getDifficultId()))
                                    .findFirst().orElse(null)
                    )
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                List<HomeworkQuestionDto> homeworkQuestionList = new ArrayList<>();
                for (QuestionVo questionVo : collect) {
                    HomeworkQuestionDto dto = new HomeworkQuestionDto();
                    BeanUtils.copyProperties(questionVo, dto);
                    dto.setOid(IdUtil.simpleUUID());
                    dto.setHomeworkOid(homework.getOid());
                    dto.setQuestionOid(questionVo.getOid());
                    dto.setId(null);
                    dto.setCreateBy(null);
                    dto.setUpdateBy(null);
                    dto.setCreateTime(null);
                    dto.setUpdateTime(null);
                    homeworkQuestionList.add(dto);
                }
                if (CollectionUtil.isNotEmpty(homeworkQuestionList)) {
                    homeworkQuestionService.saveBatch(homeworkQuestionList);
                }
            }
        }
        HomeworkVo homeworkVo = new HomeworkVo();
        BeanUtils.copyProperties(homework, homeworkVo);
        return AjaxResult.success(homeworkVo);
    }

    @Override
    public AjaxResult batchUpdateStatus(HomeworkBo homeworkBo) {
        if ((StringUtils.isEmpty(homeworkBo.getHomeworkBookOid()) && StringUtils.isEmpty(homeworkBo.getOids())) || homeworkBo.getStatus() == null) {
            return AjaxResult.fail("参数错误");
        }

        List<String> arr = new ArrayList<>();
        if (StringUtils.isNotEmpty(homeworkBo.getHomeworkBookOid())) {
            HomeworkConditionBo bo = new HomeworkConditionBo();
            bo.setHasBind("yes");
            bo.setHomeworkBookOid(homeworkBo.getHomeworkBookOid());
            bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            bo.setOrderBy("order_num asc");
            List<HomeworkVo> homeworkListByCondition = homeworkMapper.getHomeworkListByCondition(bo);
            for (HomeworkVo homeworkVo : homeworkListByCondition) {
                arr.add(homeworkVo.getOid());
            }
        }
        if (StringUtils.isNotEmpty(homeworkBo.getOids())) {
            arr = Arrays.asList(homeworkBo.getOids().split(","));
        }

        if (CollectionUtil.isEmpty(arr)) {
            return AjaxResult.success("保存成功");
        }
        LambdaUpdateWrapper<HomeworkDto> lup = new LambdaUpdateWrapper<>();
        lup.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lup.in(HomeworkDto::getOid, arr);
        lup.set(HomeworkDto::getStatus, homeworkBo.getStatus());
        if (update(new HomeworkDto(), lup)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public HomeworkVo getDetail(String oid) {
        LambdaQueryWrapper<HomeworkDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(HomeworkDto::getOid, oid);
        HomeworkDto homework = getOne(lqw);
        HomeworkVo homeworkVo = new HomeworkVo();
        if (homework != null) {
            BeanUtils.copyProperties(homework, homeworkVo);
        }
        return homeworkVo;
    }

}