package com.light.utils;

import com.light.core.entity.AjaxResult;
import com.light.core.utils.SpringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public class RedisLockUtil {

    public static AjaxResult lock(String lockKey, Function<String, AjaxResult> c) {
        RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            lock.lock(10, TimeUnit.SECONDS);
            return c.apply(lockKey);
        } catch (Exception e) {
            return AjaxResult.fail();
        } finally {
            lock.unlock();
        }
    }
}
