package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IHomeworkService extends IService<HomeworkDto> {

    List<HomeworkVo> getHomeworkListByCondition(HomeworkConditionBo condition);

    AjaxResult listBindAndNoBind(HomeworkConditionBo condition);

    String generateCode();

	AjaxResult addHomework(HomeworkBo homeworkBo);

	AjaxResult updateHomework(HomeworkBo homeworkBo);

	AjaxResult<HomeworkVo> cartHomework(String oid);

	AjaxResult<HomeworkVo> copyHomework(String oid);

    AjaxResult<HomeworkVo> originalHomework(HomeworkBo homeworkBo);

    AjaxResult<HomeworkVo> layeredHomework(HomeworkBo homeworkBo);

    AjaxResult batchUpdateStatus(HomeworkBo homeworkBo);

    HomeworkVo getDetail(String oid);

}

