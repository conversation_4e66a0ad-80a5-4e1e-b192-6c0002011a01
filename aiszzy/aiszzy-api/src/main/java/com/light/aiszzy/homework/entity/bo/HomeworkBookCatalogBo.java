package com.light.aiszzy.homework.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作业本目录
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class HomeworkBookCatalogBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 作业本目录id
	 */
	@ApiModelProperty("作业本目录id")
	private Long homeworkBookCatalogId;
	
	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 拖动oid
	 */
	private String dragOid;
	/**
	 * 拖动前一个oid
	 */
	private String dragPreOid;
	/**
	 * 作业本id
	 */
	@ApiModelProperty("作业本id")
	private String homeworkBookOid;
	/**
	 * 父节点
	 */
	@ApiModelProperty("父节点")
	private String parentOid;
	/**
	 * 目录名称
	 */
	@ApiModelProperty("目录名称")
	private String name;
	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Long orderNum;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
