package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDoubtDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultDoubtConditionBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultDoubtVo;

/**
 * 校本作业疑问表（影子表，处理作业整张卷子的疑问项）Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultDoubtMapper extends BaseMapper<HomeworkResultDoubtDto> {

	List<HomeworkResultDoubtVo> getHomeworkResultDoubtListByCondition(HomeworkResultDoubtConditionBo condition);

}
