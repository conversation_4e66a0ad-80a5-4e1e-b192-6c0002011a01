package com.light.ocr.tencent;

import cn.hutool.core.io.IoUtil;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.FormulaOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.FormulaOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.TextFormula;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
public class TencentOcrApi {

    @Resource
    private TencentOcrProperties tencentOcrProperties;

    /**
     *  公式识别
     * @param imgBase64 the img base64 图片 Base64数据
     * @return {@link TextFormula[] }
     * @throws TencentCloudSDKException
     */
    public TextFormula[] matchOcr(String imgBase64) throws TencentCloudSDKException {
        Credential cred = new Credential(tencentOcrProperties.getAccessKey(), tencentOcrProperties.getAccessSecret());
        OcrClient client = new OcrClient(cred, tencentOcrProperties.getRegion());
        FormulaOCRRequest request = new FormulaOCRRequest();
        request.setImageBase64(imgBase64);
        FormulaOCRResponse formulaOCRResponse = client.FormulaOCR(request);
        log.debug("【腾讯OCR】 腾讯公式识别， 响应:{}", FormulaOCRResponse.toJsonString(formulaOCRResponse));
        return formulaOCRResponse.getFormulaInfos();
    }
}
