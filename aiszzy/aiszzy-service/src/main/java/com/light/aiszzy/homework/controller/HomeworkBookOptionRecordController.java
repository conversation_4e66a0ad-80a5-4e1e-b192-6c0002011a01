package com.light.aiszzy.homework.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookOptionRecordBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookOptionRecordVo;
import com.light.aiszzy.homework.service.IHomeworkBookOptionRecordService;

import com.light.aiszzy.homework.api.HomeworkBookOptionRecordApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 作业本映射印送记录
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@RestController
@Validated
@Api(value = "", tags = "作业本映射印送记录接口")
public class HomeworkBookOptionRecordController implements HomeworkBookOptionRecordApi {

    @Autowired
    private IHomeworkBookOptionRecordService homeworkBookOptionRecordService;

    public AjaxResult<PageInfo<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordPageListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkBookOptionRecordVo> pageInfo = new PageInfo<>(homeworkBookOptionRecordService.getHomeworkBookOptionRecordListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkBookOptionRecordVo>> getHomeworkBookOptionRecordListByCondition(@RequestBody HomeworkBookOptionRecordConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkBookOptionRecordService.getHomeworkBookOptionRecordListByCondition(condition));
    }

    public AjaxResult addHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo) {
        return homeworkBookOptionRecordService.addHomeworkBookOptionRecord(homeworkBookOptionRecordBo);
    }

    public AjaxResult updateHomeworkBookOptionRecord(@Validated @RequestBody HomeworkBookOptionRecordBo homeworkBookOptionRecordBo) {
        if (null == homeworkBookOptionRecordBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkBookOptionRecordService.updateHomeworkBookOptionRecord(homeworkBookOptionRecordBo);
    }

    public AjaxResult<HomeworkBookOptionRecordVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkBookOptionRecordService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkBookOptionRecordBo homeworkBookOptionRecordBo = new HomeworkBookOptionRecordBo();
            homeworkBookOptionRecordBo.setOid(oid);
            homeworkBookOptionRecordBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkBookOptionRecordService.updateHomeworkBookOptionRecord(homeworkBookOptionRecordBo);
    }
}
