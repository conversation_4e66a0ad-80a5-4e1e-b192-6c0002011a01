package com.light.aiszzy.practiceBook.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教辅信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("practice_book")
public class PracticeBookDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 二维码使用，8位，字母加数字，尝试10次，重复返回报错
	 */
	@TableField("code")
	private String code;

	/**
	 * 教辅名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 教辅封面，存储图片的路径或URL
	 */
	@TableField("cover_image")
	private String coverImage;

	/**
	 * 教辅简介
	 */
	@TableField("description")
	private String description;

	/**
	 * 出版社 code, 字典转换
	 */
	@TableField("publisher")
	private String publisher;

	/**
	 * 学科CODE
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * isbn
	 */
	@TableField("isbn")
	private String isbn;

	/**
	 * 版本，存储教辅的版本信息
	 */
	@TableField("text_book_version_id")
	private Long textBookVersionId;

	/**
	 * 启用年份
	 */
	@TableField("year")
	private String year;

	/**
	 * 学期，1上学期，2下学期
	 */
	@TableField("term")
	private Integer term;

	/**
	 * 教辅系列，存储教辅所属系列（可选）
	 */
	@TableField("series")
	private String series;

	/**
	 * 教辅分类，存储教辅的分类信息
	 */
	@TableField("category")
	private String category;

	/**
	 * 可见范围，可选值为 0 public（公开）或 1 private（私有），默认为 0 public
	 */
	@TableField("visibility")
	private Integer visibility;

	/**
	 * 教辅文件，存储文件的路径或URL
	 */
	@TableField("file_url")
	private String fileUrl;

	/**
	 * 教辅文件类型，1 pdf 2 zip
	 */
	@TableField("file_type")
	private Integer fileType;

	/**
	 * 是否是市场化教辅
	 */
	@TableField("is_marketization")
	private Integer isMarketization;

	/**
	 * 是否支持高拍
	 */
	@TableField("is_high_shots")
	private Integer isHighShots;

	/**
	 * 目录文件地址
	 */
	@TableField("catalog_file_path")
	private String catalogFilePath;

	/**
	 * 目录是否设置，0否1是
	 */
	@TableField("catalog_status")
	private Integer catalogStatus;

	/**
	 * 审核状态，1未提交，2审核中 3审核成功 4打回
	 */
	@TableField("review_status")
	private Integer reviewStatus;

	/**
	 * 最后审核意见
	 */
	@TableField("review_comment")
	private String reviewComment;

	/**
	 * 状态 1下架，2上架
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 备注
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 已完成框题个数
	 */
	@TableField("finish_question_num")
	private Long finishQuestionNum;

	/**
	 * 需要框题所有个数
	 */
	@TableField("total_question_num")
	private Long totalQuestionNum;

	/**
	 * 学科网智书id，空为自建
	 */
	@TableField("xkw_zs_id")
	private String xkwZsId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
