package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业每页题目坐标信息
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
public class HomeworkPageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 题目id
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    private String code;

    /**
     * 作业id
     */
    private String homeworkOid;

    /**
     * 页码
     */
    private Long pageNo;

    /**
     * 题目信息，题号，坐标等
     */
    private String questionJson;

    /**
     * 单页图片地址
     */
    private String pageUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
