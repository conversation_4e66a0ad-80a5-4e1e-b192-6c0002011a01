package com.light.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.light.core.exception.WarningException;
import io.netty.util.internal.ThrowableUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Base64;

@Slf4j
public class ImgUtil {

    /**
     *  截取图片获取
     * @param imgUrl the img url
     * @param position the position x_y_h_w
     * @return {@link String }
     */
    public static String cutQuestionPageImg2Base64(String imgUrl, String position) {
        if(StrUtil.isEmpty(imgUrl)) {
            throw new WarningException("图片地址不能为空");
        }

        String[] positionArray = position.split("_");
        String cutImg = imgUrl.concat("?imageMogr2/cut/" + positionArray[2] + "x" + positionArray[3] + "x" + positionArray[0] + "x" + positionArray[1]);
        try(InputStream inputStream = new URL(cutImg).openStream()) {
            return Base64.getEncoder().encodeToString(IoUtil.readBytes(inputStream));
        } catch (IOException e) {
            log.error("【截取图片失败】 cutImg地址：{},  原图片地址：{},  position 地址：{}", cutImg, imgUrl, position);
            throw new WarningException("图片截取失败");
        }
    }


    public static MetaInfo getMetaInfo(byte[] imgByte) {
        try(ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imgByte)) {
            BufferedImage read = ImageIO.read(byteArrayInputStream);
            return MetaInfo.builder().width(read.getWidth()).height(read.getHeight()).build();
        }catch (IOException e ) {
            log.error("【获取图片信息】 文件获取异常:{}", ThrowableUtil.stackTraceToString(e));
        }
        return null;
    }



    @Data
    @Builder
    public static class MetaInfo {

        private int width;

        private int height;
    }
}
