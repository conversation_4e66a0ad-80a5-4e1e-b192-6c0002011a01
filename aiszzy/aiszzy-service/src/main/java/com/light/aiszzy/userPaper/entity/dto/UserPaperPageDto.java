package com.light.aiszzy.userPaper.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户上传每页图片表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_paper_page")
public class UserPaperPageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 用户上传试卷OID
	 */
	@TableField("user_paper_oid")
	private String userPaperOid;

	/**
	 * 页码
	 */
	@TableField("page_no")
	private Long pageNo;

	/**
	 * 图片地址
	 */
	@TableField("image_url")
	private String imageUrl;

	/**
	 * 修改后框题个数（实际）
	 */
	@TableField("question_num")
	private Long questionNum;

	/**
	 * 已完成框题个数
	 */
	@TableField("finish_question_num")
	private Long finishQuestionNum;

	/**
	 * 解析框题个数
	 */
	@TableField("analysis_question_num")
	private Long analysisQuestionNum;

	/**
	 * 解析结果
	 */
	@TableField("analysis_json")
	private String analysisJson;

	/**
	 * 是否完成  0：未完成 1：完成
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
