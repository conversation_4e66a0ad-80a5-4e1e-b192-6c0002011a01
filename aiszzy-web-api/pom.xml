<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>aiszzy-parent</artifactId>
        <groupId>com.light</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>com.light</groupId>
    <artifactId>aiszzy-web-api</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>AI数智作业web</name>
    <description>AI数智作业web</description>

    <dependencies>
        <dependency>
            <groupId>com.light</groupId>
            <artifactId>aiszzy-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.light</groupId>
            <artifactId>light-logger</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>2.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos-sts_api</artifactId>
            <version>3.1.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <!--打包时先排除掉每个环境的配置文件-->
                    <exclude>profiles/*</exclude>
                </excludes>
                <includes>
                    <!--如果有其他定义通用文件，需要包含进来-->
                    <!--<include></include>-->
                </includes>
            </resource>
        </resources>
        <sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
    </build>
</project>