package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.mapper.HomeworkMapper;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkBookCatalogInfoDto;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookCatalogInfoBo;
import com.light.aiszzy.homework.entity.vo.HomeworkBookCatalogInfoVo;
import com.light.aiszzy.homework.service.IHomeworkBookCatalogInfoService;
import com.light.aiszzy.homework.mapper.HomeworkBookCatalogInfoMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业本目录关联作业接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkBookCatalogInfoServiceImpl extends ServiceImpl<HomeworkBookCatalogInfoMapper, HomeworkBookCatalogInfoDto> implements IHomeworkBookCatalogInfoService {

	@Resource
	private HomeworkBookCatalogInfoMapper homeworkBookCatalogInfoMapper;

	@Resource
	private HomeworkMapper homeworkMapper;


	@Override
	public List<HomeworkBookCatalogInfoVo> getHomeworkBookCatalogInfoListByCondition(HomeworkBookCatalogInfoConditionBo condition) {
        return homeworkBookCatalogInfoMapper.getHomeworkBookCatalogInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		HomeworkBookCatalogInfoDto homeworkBookCatalogInfo = new HomeworkBookCatalogInfoDto();
		BeanUtils.copyProperties(homeworkBookCatalogInfoBo, homeworkBookCatalogInfo);
		homeworkBookCatalogInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkBookCatalogInfo.setOid(IdUtil.simpleUUID());
		if(save(homeworkBookCatalogInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		LambdaQueryWrapper<HomeworkBookCatalogInfoDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookCatalogInfoDto::getOid, homeworkBookCatalogInfoBo.getOid());
		HomeworkBookCatalogInfoDto homeworkBookCatalogInfo = getOne(lqw);
		Long id = homeworkBookCatalogInfo.getId();
		if(homeworkBookCatalogInfo == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkBookCatalogInfoBo, homeworkBookCatalogInfo);
		homeworkBookCatalogInfo.setId(id);
		if(updateById(homeworkBookCatalogInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult bind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		if (StringUtils.isEmpty(homeworkBookCatalogInfoBo.getHomeworkBookCatalogOids())) {
			return AjaxResult.fail("保存失败");
		}
		List<HomeworkBookCatalogInfoDto> arr = new ArrayList<>();
		List<String> homeworkArr = new ArrayList<>();
		for (String cw : homeworkBookCatalogInfoBo.getHomeworkBookCatalogOids().split(",")) {
			String[] s = cw.split("-");
			HomeworkBookCatalogInfoDto homeworkBookCatalogInfo = new HomeworkBookCatalogInfoDto();
			homeworkBookCatalogInfo.setHomeworkBookCatalogOid(s[0]);
			homeworkBookCatalogInfo.setHomeworkOid(s[1]);
			homeworkBookCatalogInfo.setOid(IdUtil.simpleUUID());
			arr.add(homeworkBookCatalogInfo);
			homeworkArr.add(homeworkBookCatalogInfo.getHomeworkOid());
		}
		if (CollectionUtil.isEmpty(arr)) {
			return AjaxResult.fail("保存失败");
		}
		List<HomeworkBookCatalogInfoDto> homeworkDtos = homeworkBookCatalogInfoMapper.selectList(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
				.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.in(HomeworkBookCatalogInfoDto::getHomeworkOid, homeworkArr));

		if (CollectionUtil.isNotEmpty(homeworkDtos)) {
			return AjaxResult.fail("存在作业已经绑定");
		}

		if (saveBatch(arr)) {
			homeworkMapper.update(null, new LambdaUpdateWrapper<HomeworkDto>()
					.in(HomeworkDto::getOid, homeworkArr)
					.set(HomeworkDto::getHomeworkBookOid, homeworkBookCatalogInfoBo.getHomeworkOid())
					.set(HomeworkDto::getIsUse, 2));
			arr.forEach(obj -> {
				obj.setOrderNum(obj.getId().intValue());
			});
			updateBatchById(arr);
			return AjaxResult.success("保存成功");
		} else {
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult unbind(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		if (StringUtils.isEmpty(homeworkBookCatalogInfoBo.getHomeworkBookCatalogOids())) {
			return AjaxResult.fail("保存失败");
		}
		for (String cw : homeworkBookCatalogInfoBo.getHomeworkBookCatalogOids().split(",")) {
			String[] s = cw.split("-");
			LambdaUpdateWrapper<HomeworkBookCatalogInfoDto> upd = new LambdaUpdateWrapper<>();
			upd.eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, s[0]);
			upd.eq(HomeworkBookCatalogInfoDto::getHomeworkOid, s[1]);
			upd.set(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.ISDELETE.getCode());
			update(null,upd);
			homeworkMapper.update(null, new LambdaUpdateWrapper<HomeworkDto>()
					.eq(HomeworkDto::getOid, s[1])
					.set(HomeworkDto::getHomeworkBookOid, null)
					.set(HomeworkDto::getIsUse, StatusEnum.NO.getCode()));
		}

		return AjaxResult.success("保存成功");
	}

	@Override
	public AjaxResult sortHomeworkBookCatalogInfo(HomeworkBookCatalogInfoBo homeworkBookCatalogInfoBo) {
		if (StringUtils.isEmpty(homeworkBookCatalogInfoBo.getDragOid())) {
			return AjaxResult.fail("保存失败");
		}
		HomeworkBookCatalogInfoDto info1 = homeworkBookCatalogInfoMapper.selectOne(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
				.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
				.eq(HomeworkBookCatalogInfoDto::getHomeworkOid, homeworkBookCatalogInfoBo.getDragOid()).last(" limit 1"));

		if (info1 == null) {
			return AjaxResult.fail("绑定目录作业查询错误");
		}
		HomeworkBookCatalogInfoDto info2 = null;
		if (StringUtils.isEmpty(homeworkBookCatalogInfoBo.getDragPreOid())) {
			info2 = homeworkBookCatalogInfoMapper.selectOne(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
					.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
					.eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, info1.getHomeworkBookCatalogOid())
					.orderByAsc(HomeworkBookCatalogInfoDto::getOrderNum).last(" limit 1"));
			if (info2 == null || info2.equals(info1)) {
				return AjaxResult.fail("绑定目录作业查询错误");
			}
		} else {
			info2 = homeworkBookCatalogInfoMapper.selectOne(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
					.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
					.eq(HomeworkBookCatalogInfoDto::getHomeworkOid, homeworkBookCatalogInfoBo.getDragPreOid()).last(" limit 1"));
			if (info2 == null) {
				return AjaxResult.fail("绑定目录作业查询错误");
			}
		}

		Integer orderNum1 = info1.getOrderNum();
		Integer orderNum2 = info2.getOrderNum();

		if (orderNum1 < orderNum2) {
			List<HomeworkBookCatalogInfoDto> effectDtos = homeworkBookCatalogInfoMapper.selectList(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
					.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
					.eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, info1.getHomeworkBookCatalogOid())
					.between(HomeworkBookCatalogInfoDto::getOrderNum, orderNum1, orderNum2)
					.orderByAsc(HomeworkBookCatalogInfoDto::getOrderNum));
			Integer orderNum3 = 0;
			List<HomeworkBookCatalogInfoDto> arr = new ArrayList<>();
			for (HomeworkBookCatalogInfoDto dto : effectDtos) {
				if (dto.getOid().equals(info1.getOid())) {
					orderNum3 = dto.getOrderNum();
				} else {
					Integer tmp = orderNum3;
					orderNum3 = dto.getOrderNum();
					dto.setOrderNum(tmp);
					arr.add(dto);
				}
			}
			info1.setOrderNum(orderNum3);
			arr.add(info1);
			updateBatchById(arr);
		} else {
			List<HomeworkBookCatalogInfoDto> effectDtos = homeworkBookCatalogInfoMapper.selectList(new LambdaQueryWrapper<HomeworkBookCatalogInfoDto>()
					.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode())
					.eq(HomeworkBookCatalogInfoDto::getHomeworkBookCatalogOid, info1.getHomeworkBookCatalogOid())
					.between(HomeworkBookCatalogInfoDto::getOrderNum, orderNum2, orderNum1)
					.orderByDesc(HomeworkBookCatalogInfoDto::getOrderNum));
			if (StringUtils.isNotEmpty(homeworkBookCatalogInfoBo.getDragPreOid())) {
				effectDtos.remove(info2);
			}
			Integer orderNum3 = 0;
			List<HomeworkBookCatalogInfoDto> arr = new ArrayList<>();
			for (HomeworkBookCatalogInfoDto dto : effectDtos) {
				if (dto.getOid().equals(info1.getOid())) {
					orderNum3 = dto.getOrderNum();
				} else {
					Integer tmp = orderNum3;
					orderNum3 = dto.getOrderNum();
					dto.setOrderNum(tmp);
					arr.add(dto);
				}
			}
			info1.setOrderNum(orderNum3);
			arr.add(info1);
			updateBatchById(arr);
		}

		return AjaxResult.success("保存成功");
	}

	@Override
	public HomeworkBookCatalogInfoVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkBookCatalogInfoDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkBookCatalogInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkBookCatalogInfoDto::getOid, oid);
		HomeworkBookCatalogInfoDto homeworkBookCatalogInfo = getOne(lqw);
	    HomeworkBookCatalogInfoVo homeworkBookCatalogInfoVo = new HomeworkBookCatalogInfoVo();
		if(homeworkBookCatalogInfo != null){
			BeanUtils.copyProperties(homeworkBookCatalogInfo, homeworkBookCatalogInfoVo);
		}
		return homeworkBookCatalogInfoVo;
	}

}