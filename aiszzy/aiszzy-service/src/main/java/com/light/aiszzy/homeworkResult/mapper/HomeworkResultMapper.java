package com.light.aiszzy.homeworkResult.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;

/**
 * 校本作业结果表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface HomeworkResultMapper extends BaseMapper<HomeworkResultDto> {

	List<HomeworkResultVo> getHomeworkResultListByCondition(HomeworkResultConditionBo condition);

}
