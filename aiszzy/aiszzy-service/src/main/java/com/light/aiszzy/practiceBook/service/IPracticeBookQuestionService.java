package com.light.aiszzy.practiceBook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.practiceBook.entity.dto.PracticeBookQuestionDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.WarningException;

import java.util.List;

/**
 * 教辅题目表，用于存储题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IPracticeBookQuestionService extends IService<PracticeBookQuestionDto> {

    List<PracticeBookQuestionVo> getPracticeBookQuestionListByCondition(PracticeBookQuestionConditionBo condition);

	AjaxResult<PracticeBookQuestionVo> addPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo);

	AjaxResult updatePracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo);

    PracticeBookQuestionVo getDetail(String oid);

	/**
	 * 完成标注功能
	 * @param practiceBookQuestionBo
	 * @return
	 */
	AjaxResult markPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 取消划题功能
	 * @param practiceBookQuestionBo
	 * @return
	 */
	AjaxResult cancelPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo);

	/**
	 * 查询相似题：根据题目oid、难度，查询出N个指定的学科网题目，实际是查询学科网相似题
	 * @param practiceBookQuestionChangeBo
	 * @return
	 */
	AjaxResult querySimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo);

	/**
	 * 设为相似题：使用指定的JSON格式题目替换当前题目
	 * @param practiceBookQuestionChangeBo
	 * @return
	 */
	AjaxResult setSimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo);

	/**
	 *  根据教辅图片 OID 删除原有数据，并批量保存数据
	 * @param practiceBookPageOid the practice book page oid 教辅 OID
	 * @param list the practice book question 教辅题目关联表
	 * @return {@link AjaxResult }
	 */
	AjaxResult delAndSaveBatchByPracticeBookPageOid(String practiceBookPageOid, List<PracticeBookQuestionBo> list);

	/**
	 * 根据教辅页码 OID 删除题目信息
	 * @param practiceBookPageOid the practice book page oid 页码 OID
	 * @return boolean
	 */
	boolean deleteByPracticeBookPageOid(String practiceBookPageOid);

	/**
	 *  根据教辅 OID 删除题目信息
	 * @param practiceBookOid the pratice book oid 教辅 OID
	 * @return boolean
	 */
	boolean deleteByPracticeBookOid(String practiceBookOid);

	/**
	 * 根据 OID 获取数据
	 * @param oid the practice book quesiton oid
	 * @return {@link PracticeBookQuestionVo }
	 */
	PracticeBookQuestionVo queryByOid(String oid);


	/**
	 * 根据传入的 position 字段截取图片，转换为 Base64 格式，获取题目信息，并更新至 practiceBookQuestion 表。
	 * <p>
	 * 该方法的主要流程如下：
	 * 1. 使用 Base64 数据查询题目信息，并获取相似题目信息。
	 * 2. 将查询到的题目信息及相似题信息转换为所需格式。
	 * 3. 更新题目数据（包括题目 JSON 和相似度）到 practiceBookQuestion 表中。
	 *
	 * @param oid      题目 OID，用于唯一标识 practiceBookQuestion 表中的题目记录。
	 * @param practiceBookPageOid  页 OID    因题目跨页问题，一个题目信息会存储在多个页面，故用于确认某个页面的操作
	 * @param position bse64截图的位置信息 x_y_宽_高，用于截取图片信息
	 * @throws WarningException 当题目信息或所属教辅信息不存在时抛出。
	 */
	PracticeBookQuestionVo processAndUpdateQuestionByPositionImg(String oid,String practiceBookPageOid, String position);

	/**
	 * 根据传入的 position 字段截取图片，转换为 Base64 格式，获取题目信息，并更新至 practiceBookQuestion 表。
	 * <p>
	 * 该方法的主要流程如下：
	 * 1. 使用 Base64 数据查询题目信息，并获取相似题目信息。
	 * 2. 将查询到的题目信息及相似题信息转换为所需格式。
	 * 3. 更新题目数据（包括题目 JSON 和相似度）到 practiceBookQuestion 表中。
	 *
	 * @param vo       practiceBookQuestion 表中的题目记录。
	 * @param practiceBookPageOid  页 OID    因题目跨页问题，一个题目信息会存储在多个页面，故用于确认某个页面的操作
	 * @param position bse64截图的位置信息 x_y_宽_高，用于截取图片信息
	 * @throws WarningException 当题目信息或所属教辅信息不存在时抛出。
	 */
	PracticeBookQuestionVo processAndUpdateQuestionByPositionImg(PracticeBookQuestionVo vo,String practiceBookPageOid, String position);
}

