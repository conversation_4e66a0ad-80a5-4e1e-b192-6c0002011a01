package com.light.aiszzy.question.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.entity.bo.QuestionConditionBo;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.aiszzy.question.service.IQuestionService;
import com.light.aiszzy.question.mapper.QuestionMapper;
import com.light.core.entity.AjaxResult;
/**
 * 题目表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, QuestionDto> implements IQuestionService {

	@Resource
	private QuestionMapper questionMapper;
	
    @Override
	public List<QuestionVo> getQuestionListByCondition(QuestionConditionBo condition) {
        return questionMapper.getQuestionListByCondition(condition);
	}

	@Override
	public AjaxResult addQuestion(QuestionBo questionBo) {
		QuestionDto question = new QuestionDto();
		BeanUtils.copyProperties(questionBo, question);
		question.setIsDelete(StatusEnum.NOTDELETE.getCode());
		question.setOid(IdUtil.simpleUUID());
		if(save(question)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateQuestion(QuestionBo questionBo) {
		LambdaQueryWrapper<QuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionDto::getOid, questionBo.getOid());
		QuestionDto question = getOne(lqw);
		Long id = question.getId();
		if(question == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(questionBo, question);
		question.setId(id);
		if(updateById(question)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public QuestionVo getDetail(String oid) {
		LambdaQueryWrapper<QuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionDto::getOid, oid);
		QuestionDto question = getOne(lqw);
	    QuestionVo questionVo = new QuestionVo();
		if(question != null){
			BeanUtils.copyProperties(question, questionVo);
		}
		return questionVo;
	}

	@Override
	public String getLatestQuestionOidByInsideLink(String insideSourceType, String insideLinkOid) {
		if (insideSourceType == null || insideSourceType.trim().isEmpty() ||
			insideLinkOid == null || insideLinkOid.trim().isEmpty()) {
			return null;
		}

		LambdaQueryWrapper<QuestionDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionDto::getInsideSourceType, insideSourceType);
		lqw.eq(QuestionDto::getInsideLinkOid, insideLinkOid);
		lqw.orderByDesc(QuestionDto::getCreateTime);
		lqw.last("LIMIT 1");

		QuestionDto questionDto = getOne(lqw);
		return questionDto != null ? questionDto.getOid() : null;
	}

}