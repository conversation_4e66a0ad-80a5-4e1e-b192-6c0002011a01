package com.light.aiszzy.practiceBook.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.practiceBook.api.PracticeBookQuestionApi;
import com.light.aiszzy.practiceBook.entity.bean.PracticeBookPositionQuestion;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionChangeBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookQuestionConditionBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookPageVo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookPageService;
import com.light.aiszzy.practiceBook.service.IPracticeBookQuestionService;
import com.light.aiszzy.question.entity.vo.QuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.WarningException;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * 教辅题目表，用于存储题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@RestController
@Validated
@Api(value = "", tags = "教辅题目表，用于存储题目信息接口")
public class PracticeBookQuestionController implements PracticeBookQuestionApi {

    @Autowired
    private IPracticeBookQuestionService practiceBookQuestionService;

    @Resource
    private IPracticeBookPageService practiceBookPageService;


    public AjaxResult<PageInfo<PracticeBookQuestionVo>>
        getPracticeBookQuestionPageListByCondition(@RequestBody PracticeBookQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<PracticeBookQuestionVo> pageInfo =
            new PageInfo<>(practiceBookQuestionService.getPracticeBookQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
            condition.getPageSize());
    }

    public AjaxResult<List<PracticeBookQuestionVo>>
        getPracticeBookQuestionListByCondition(@RequestBody PracticeBookQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(practiceBookQuestionService.getPracticeBookQuestionListByCondition(condition));
    }

    public AjaxResult saveDataByPosition(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        String practiceBookPageOid = practiceBookQuestionBo.getPracticeBookPageOid();
        if(StrUtil.isEmpty(practiceBookPageOid)) {
            return AjaxResult.fail("教辅页码OID不能为空");
        }
        String position = practiceBookQuestionBo.getPosition();
        if(StrUtil.isEmpty(position)) {
            return AjaxResult.fail("坐标不能为空");
        }
        AjaxResult<PracticeBookQuestionVo> ajaxResult = this.practiceBookQuestionService.addPracticeBookQuestion(practiceBookQuestionBo);
        if(ajaxResult.isFail()) {
            return ajaxResult;
        }
        PracticeBookQuestionVo data = ajaxResult.getData();
        PracticeBookQuestionVo practiceBookQuestionVo = this.practiceBookQuestionService.processAndUpdateQuestionByPositionImg(data,practiceBookPageOid, position);
        return AjaxResult.success(practiceBookQuestionVo);
    }

    public AjaxResult
        updatePracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        if (null == practiceBookQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookQuestionService.updatePracticeBookQuestion(practiceBookQuestionBo);
    }

    public AjaxResult<PracticeBookQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(practiceBookQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
        PracticeBookQuestionBo practiceBookQuestionBo = new PracticeBookQuestionBo();
        practiceBookQuestionBo.setOid(oid);
        practiceBookQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return practiceBookQuestionService.updatePracticeBookQuestion(practiceBookQuestionBo);
    }

    public AjaxResult markPracticeBookQuestion(@Validated @RequestBody PracticeBookQuestionBo practiceBookQuestionBo) {
        if (null == practiceBookQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookQuestionService.markPracticeBookQuestion(practiceBookQuestionBo);
    }

    @Override
    public AjaxResult cancelPracticeBookQuestion(PracticeBookQuestionBo practiceBookQuestionBo) {
        if (null == practiceBookQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return practiceBookQuestionService.cancelPracticeBookQuestion(practiceBookQuestionBo);
    }

    @Override
    public AjaxResult querySimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        if (null == practiceBookQuestionChangeBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        if (CollectionUtils.isEmpty(practiceBookQuestionChangeBo.getDifficultyLevels())) {
            return AjaxResult.fail("难度列表不能为空");
        }
        return practiceBookQuestionService.querySimilarQuestion(practiceBookQuestionChangeBo);
    }


    @Override
    public AjaxResult<PracticeBookQuestionVo> getOrInitQuestionInfoByOid(@PathVariable("oid") String oid, @RequestBody PracticeBookQuestionBo bo) {

        // 获取题目信息
        PracticeBookQuestionVo vo = Optional.ofNullable(this.practiceBookQuestionService.queryByOid(oid)).orElseThrow(()-> new WarningException("题目信息不存在"));

        String position = bo.getPosition();
        // 当前操作页 OID
        String practiceBookPageOid = bo.getPracticeBookPageOid();

        PracticeBookPageVo pageVo = Optional.ofNullable(this.practiceBookPageService.queryByOid(practiceBookPageOid)).orElseThrow(()-> new WarningException("页码不存在"));
        List<PracticeBookPositionQuestion> positionQuestionList = pageVo.getPositionQuestionList();

        PracticeBookPositionQuestion positionQuestion = positionQuestionList.stream()
                .filter(x -> x.getPracticeBookQuestionOid().equalsIgnoreCase(oid))
                .findAny().orElseThrow(()-> new WarningException("该操作页未获取到该题目信息"));


        // 传入的坐标点与原坐标点信息一致 && 题目信息 json 已有数据   ，直接返回数据
        String questionContentJson = vo.getQuestionContentJson();
        if(positionQuestion.getPosition().equals(position) && StrUtil.isNotEmpty(questionContentJson)){
            return AjaxResult.success(vo);
        }
        PracticeBookQuestionVo practiceBookQuestionVo = this.practiceBookQuestionService.processAndUpdateQuestionByPositionImg(vo,practiceBookPageOid, position);
        return AjaxResult.success(practiceBookQuestionVo);
    }



    @Override
    public AjaxResult<PracticeBookQuestionVo> reFetchQuestionInfoByOid(@RequestBody PracticeBookQuestionBo bo) {
        String oid = bo.getOid();
        String practiceBookPageOid = bo.getPracticeBookPageOid();
        String position = bo.getPosition();
        // 更新题目题目信息
        PracticeBookQuestionVo practiceBookQuestionVo = this.practiceBookQuestionService.processAndUpdateQuestionByPositionImg(oid,practiceBookPageOid,  position);
        return AjaxResult.success(practiceBookQuestionVo);
    }

    @Override
    public AjaxResult setSimilarQuestion(PracticeBookQuestionChangeBo practiceBookQuestionChangeBo) {
        if (null == practiceBookQuestionChangeBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        if (StringUtils.isBlank(practiceBookQuestionChangeBo.getSimilarQuestionJson())) {
            return AjaxResult.fail("相似题目JSON数据不能为空");
        }
        return practiceBookQuestionService.setSimilarQuestion(practiceBookQuestionChangeBo);
    }
}
