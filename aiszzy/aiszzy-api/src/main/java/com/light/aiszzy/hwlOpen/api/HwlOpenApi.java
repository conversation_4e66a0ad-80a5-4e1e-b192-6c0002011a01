package com.light.aiszzy.hwlOpen.api;

import com.light.beans.ImageRequestBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 好未来开放平台接口
 *
 * <AUTHOR>
 * @date 2025/3/17 17:07
 */
public interface HwlOpenApi {

    /**
     * 好未来题目框选（拍搜）
     *
     * @param imageRequestBo 图片请求参数
     * @return 框选结果
     */
    @PostMapping("/hwl-open/automatic-box")
    @ApiOperation(value = "好未来题目框选（拍搜）", httpMethod = "POST")
    @ApiImplicitParam(name = "imageRequestBo", value = "图片请求参数", required = true, dataType = "ImageRequestBo")
    AjaxResult automaticBox(@RequestBody ImageRequestBo imageRequestBo);
}
