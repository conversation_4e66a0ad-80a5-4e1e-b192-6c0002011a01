package com.light.aiszzy.hwlOpen.controller;

import com.light.beans.ImageRequestBo;
import com.light.aiszzy.hwlOpen.service.HwlService;
import com.light.aiszzy.hwlOpen.api.HwlOpenApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;


/**
 * 好未来开放平台接口控制器
 *
 * 提供好未来AI图像识别和题目框选功能
 * 使用Java 8特性优化代码结构和异常处理
 *
 * <AUTHOR>
 * @date 2025/3/17 17:07
 */
@RestController
@RequestMapping("/hwl-open")
@Slf4j
@Api("好未来开放平台接口")
public class HwlOpenController implements HwlOpenApi {

    @Resource
    private HwlService hwlService;

    /**
     * 好未来题目框选（拍搜）
     *
     * 使用Optional和函数式编程优化参数验证和异常处理
     * API文档: https://openai.100tal.com/documents/article/page?id=177
     *
     * @param imageRequestBo 图片请求参数
     * @return 框选结果
     */
    @ApiOperation("好未来题目框选（拍搜）")
    @PostMapping("/automatic-box")
    public AjaxResult automaticBox(@RequestBody ImageRequestBo imageRequestBo) {
        return Optional.ofNullable(imageRequestBo)
            .filter(this::validateImageRequest)
            .map(hwlService::processAutomaticBox)
            .orElse(AjaxResult.fail("参数错误或图片不能为空！"));
    }

    /**
     * 验证图片请求参数
     *
     * @param request 图片请求对象
     * @return 验证结果
     */
    private boolean validateImageRequest(ImageRequestBo request) {
        return request.getImageBase64() != null || request.getImageUrl() != null;
    }

}
